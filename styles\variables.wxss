/* 全局CSS变量定义 */
page {
  /* 主题色彩 */
  --primary-color: #4CAF50;
  --primary-light: #81C784;
  --primary-dark: #388E3C;
  
  --secondary-color: #2196F3;
  --secondary-light: #64B5F6;
  --secondary-dark: #1976D2;
  
  /* 状态色彩 */
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --danger-color: #F44336;
  --info-color: #2196F3;
  
  /* 文本色彩 */
  --text-color: #333333;
  --text-secondary: #666666;
  --text-muted: #999999;
  --text-light: #CCCCCC;
  --text-white: #FFFFFF;
  
  /* 背景色彩 */
  --bg-color: #F5F5F5;
  --bg-white: #FFFFFF;
  --bg-gray: #F8F9FA;
  --bg-dark: #343A40;
  
  /* 边框色彩 */
  --border-color: #E0E0E0;
  --border-light: #F0F0F0;
  --border-dark: #CCCCCC;
  
  /* 字体大小 */
  --font-size-xs: 20rpx;
  --font-size-sm: 24rpx;
  --font-size-base: 28rpx;
  --font-size-lg: 32rpx;
  --font-size-xl: 36rpx;
  --font-size-xxl: 40rpx;
  
  /* 字体粗细 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 600;
  
  /* 间距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-base: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 48rpx;
  --spacing-xxl: 64rpx;
  
  /* 圆角 */
  --border-radius-sm: 4rpx;
  --border-radius-base: 8rpx;
  --border-radius-lg: 12rpx;
  --border-radius-xl: 16rpx;
  --border-radius-round: 50%;
  
  /* 阴影 */
  --shadow-sm: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  --shadow-base: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 12rpx 24rpx rgba(0, 0, 0, 0.15);
  
  /* 层级 */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  
  /* 动画时间 */
  --transition-fast: 0.15s;
  --transition-base: 0.3s;
  --transition-slow: 0.5s;
  
  /* 动画函数 */
  --ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  --ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
  
  /* 容器宽度 */
  --container-sm: 576rpx;
  --container-md: 768rpx;
  --container-lg: 992rpx;
  --container-xl: 1200rpx;
  
  /* 头部高度 */
  --header-height: 88rpx;
  --navbar-height: 44rpx;
  --tabbar-height: 98rpx;
  
  /* 按钮高度 */
  --btn-height-sm: 56rpx;
  --btn-height-base: 72rpx;
  --btn-height-lg: 88rpx;
  
  /* 输入框高度 */
  --input-height-sm: 56rpx;
  --input-height-base: 72rpx;
  --input-height-lg: 88rpx;
}
