# 微信小程序敏捷开发框架 (WxPro)

一套功能完整、开箱即用的微信小程序敏捷开发框架，专为智慧农场管理系统设计，但可扩展应用于各种业务场景。

## ✨ 特性

- 🚀 **开箱即用** - 完整的项目结构和配置
- 🎨 **统一样式系统** - 基于CSS变量的主题系统
- 📱 **响应式设计** - 适配各种屏幕尺寸
- 🔧 **全局状态管理** - 轻量级状态管理方案
- 🌐 **网络请求封装** - 统一的API调用和错误处理
- 🧩 **组件化开发** - 可复用的UI组件库
- 🛠 **工具函数库** - 常用工具方法集合
- 📊 **数据可视化** - 图表和统计展示
- 🔐 **权限管理** - 用户认证和授权
- 📦 **模块化架构** - 清晰的代码组织结构

## 📁 项目结构

```
wxpro/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── sitemap.json          # 站点地图配置
├── project.config.json   # 项目配置文件
├── 
├── api/                  # API接口管理
│   └── index.js         # 统一API接口
├── 
├── config/              # 全局配置
│   └── index.js        # 配置文件
├── 
├── utils/               # 工具函数库
│   ├── store.js        # 全局状态管理
│   ├── request.js      # 网络请求封装
│   ├── common.js       # 通用工具函数
│   └── date.js         # 日期处理工具
├── 
├── styles/              # 样式系统
│   ├── variables.wxss  # CSS变量定义
│   ├── common.wxss     # 通用样式类
│   └── theme.wxss      # 主题样式
├── 
├── components/          # 组件库
│   └── common/         # 通用组件
├── 
├── pages/               # 页面文件
│   ├── index/          # 首页（总览）
│   ├── tasks/          # 任务管理
│   ├── workspace/      # 工作台
│   ├── messages/       # 消息中心
│   └── profile/        # 个人中心
└── 
└── assets/              # 静态资源
    ├── images/         # 图片资源
    └── icons/          # 图标资源
```

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd wxpro
```

### 2. 配置小程序

1. 在微信开发者工具中导入项目
2. 修改 `project.config.json` 中的 `appid`
3. 根据需要修改 `config/index.js` 中的配置

### 3. 启动开发

在微信开发者工具中点击"编译"即可开始开发。

## 📖 核心功能

### 全局状态管理

使用内置的状态管理系统：

```javascript
import store from '../utils/store.js'

// 设置状态
store.setState({
  user: {
    name: '张三',
    role: 'admin'
  }
})

// 获取状态
const userName = store.getState('user.name')

// 订阅状态变化
store.subscribe('user', (userData) => {
  console.log('用户数据更新:', userData)
})
```

### 网络请求

统一的网络请求封装：

```javascript
import api from '../api/index.js'

// GET请求
api.user.getUserInfo().then(response => {
  console.log(response.data)
})

// POST请求
api.task.createTask({
  name: '新任务',
  description: '任务描述'
}).then(response => {
  console.log('任务创建成功')
})
```

### 样式系统

基于CSS变量的主题系统：

```css
/* 使用预定义的变量 */
.my-component {
  background-color: var(--primary-color);
  padding: var(--spacing-base);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
}

/* 使用通用样式类 */
<view class="card flex-center p-3 mb-2">
  <text class="text-primary text-lg">标题</text>
</view>
```

### 工具函数

丰富的工具函数库：

```javascript
import { formatDate, showToast, debounce } from '../utils/common.js'

// 日期格式化
const formattedDate = formatDate(new Date(), 'YYYY-MM-DD HH:mm')

// 显示提示
showToast('操作成功', 'success')

// 防抖函数
const debouncedSearch = debounce(function(keyword) {
  // 搜索逻辑
}, 500)
```

## 🎨 样式系统

### CSS变量

框架提供了完整的CSS变量系统，包括：

- **颜色变量**: 主题色、状态色、文本色等
- **尺寸变量**: 字体大小、间距、圆角等
- **阴影变量**: 不同层级的阴影效果
- **动画变量**: 过渡时间和缓动函数

### 通用样式类

提供了大量通用样式类：

- **布局类**: `flex`, `flex-center`, `flex-between` 等
- **间距类**: `p-1`, `m-2`, `mt-3`, `mb-4` 等
- **文本类**: `text-center`, `text-primary`, `text-lg` 等
- **背景类**: `bg-white`, `bg-primary`, `bg-gray` 等

## 🔧 配置说明

### 全局配置 (config/index.js)

```javascript
const config = {
  // 环境配置
  env: 'development',
  
  // API配置
  api: {
    baseUrl: 'https://api.example.com',
    timeout: 10000
  },
  
  // 主题配置
  theme: {
    primaryColor: '#4CAF50',
    secondaryColor: '#2196F3'
  },
  
  // 功能开关
  features: {
    debug: true,
    errorReport: true
  }
}
```

### 页面配置

每个页面都有对应的 `.json` 配置文件：

```json
{
  "navigationBarTitleText": "页面标题",
  "enablePullDownRefresh": true,
  "onReachBottomDistance": 50
}
```

## 📱 页面说明

### 首页 (pages/index)
- 天气信息展示
- 农场统计数据
- 种植规模图表
- 快捷操作入口

### 任务管理 (pages/tasks)
- 任务列表展示
- 搜索和筛选
- 任务状态管理
- 进度跟踪

### 工作台 (pages/workspace)
- 功能模块导航
- 数据统计展示
- 快捷工具集合

### 消息中心 (pages/messages)
- 消息分类展示
- 消息搜索
- 已读/未读管理

### 个人中心 (pages/profile)
- 用户信息展示
- 功能设置
- 数据统计

## 🛠 开发指南

### 添加新页面

1. 在 `pages/` 目录下创建新的页面文件夹
2. 创建 `.wxml`, `.js`, `.wxss`, `.json` 四个文件
3. 在 `app.json` 中注册新页面
4. 根据需要添加到 `tabBar` 配置中

### 添加新API

1. 在 `api/index.js` 中添加新的API方法
2. 使用统一的请求封装
3. 处理错误和加载状态

### 添加新组件

1. 在 `components/` 目录下创建组件文件夹
2. 创建组件的四个文件
3. 在需要使用的页面中引入组件

### 样式开发

1. 优先使用CSS变量和通用样式类
2. 遵循BEM命名规范
3. 考虑响应式适配

## 🔍 最佳实践

### 代码组织
- 保持文件结构清晰
- 使用有意义的命名
- 添加必要的注释

### 性能优化
- 合理使用分页加载
- 图片懒加载
- 避免频繁的setData操作

### 用户体验
- 提供加载状态反馈
- 处理网络异常情况
- 保持界面响应流畅

### 错误处理
- 统一的错误处理机制
- 用户友好的错误提示
- 错误日志记录

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个框架。

## 📞 支持

如有问题，请通过以下方式联系：

- 提交 GitHub Issue
- 发送邮件至: <EMAIL>

---

**WxPro** - 让小程序开发更高效！
