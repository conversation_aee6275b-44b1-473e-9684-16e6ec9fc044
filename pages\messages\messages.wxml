<!-- 消息页面 -->
<view class="container">
  <!-- 消息分类标签 -->
  <view class="message-tabs">
    <view class="tab-item {{currentTab === 'all' ? 'active' : ''}}" 
          bindtap="switchTab" data-tab="all">
      <text>进度异常</text>
    </view>
    <view class="tab-item {{currentTab === 'system' ? 'active' : ''}}" 
          bindtap="switchTab" data-tab="system">
      <text>巡田异常</text>
    </view>
    <view class="tab-item {{currentTab === 'weather' ? 'active' : ''}}" 
          bindtap="switchTab" data-tab="weather">
      <text>气象灾害</text>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-input-wrapper">
      <image class="search-icon" src="/assets/icons/search.png" mode="aspectFit"></image>
      <input class="search-input" 
             placeholder="搜索消息内容" 
             value="{{searchKeyword}}" 
             bindinput="onSearchInput" />
      <view wx:if="{{searchKeyword}}" class="search-clear" bindtap="onSearchClear">
        <image src="/assets/icons/close.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 消息列表 -->
  <view class="message-list">
    <view wx:for="{{messageList}}" wx:key="id" 
          class="message-item {{!item.read ? 'unread' : ''}}"
          bindtap="onMessageTap" 
          data-message="{{item}}">
      
      <!-- 消息类型图标 -->
      <view class="message-icon">
        <image wx:if="{{item.type === 'warning'}}" src="/assets/icons/warning.png" mode="aspectFit"></image>
        <image wx:if="{{item.type === 'info'}}" src="/assets/icons/info.png" mode="aspectFit"></image>
        <image wx:if="{{item.type === 'success'}}" src="/assets/icons/success.png" mode="aspectFit"></image>
        <image wx:if="{{item.type === 'error'}}" src="/assets/icons/error.png" mode="aspectFit"></image>
      </view>

      <!-- 消息内容 -->
      <view class="message-content">
        <view class="message-title">{{item.title}}</view>
        <view class="message-text">{{item.content}}</view>
        <view class="message-time">{{item.time}}</view>
      </view>

      <!-- 未读标识 -->
      <view wx:if="{{!item.read}}" class="unread-dot"></view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{!loading && messageList.length === 0}}" class="empty">
    <view class="empty-icon">📬</view>
    <view class="empty-text">{{searchKeyword ? '未找到相关消息' : '暂无消息'}}</view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 底部操作栏 -->
<view class="bottom-actions" wx:if="{{messageList.length > 0}}">
  <view class="action-item" bindtap="markAllRead">
    <image src="/assets/icons/read-all.png" mode="aspectFit"></image>
    <text>全部已读</text>
  </view>
  
  <view class="action-item" bindtap="deleteAll">
    <image src="/assets/icons/delete.png" mode="aspectFit"></image>
    <text>清空消息</text>
  </view>
</view>
