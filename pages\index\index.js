// 首页 - 总览页面
import api from '../../api/index.js'
import store from '../../utils/store.js'
import { formatDate } from '../../utils/date.js'
import { showToast } from '../../utils/common.js'

const app = getApp()

Page({
  data: {
    // 基础信息
    location: '甘肃省酒泉市金塔县东坝镇',
    farmName: '金桥农场',
    
    // 天气信息
    weather: {
      temperature: '25.1°C',
      condition: '小雨',
      humidity: '湿度91%',
      date: '2025/06/11 五月十六'
    },
    
    // 农场统计数据
    farmStats: {
      machinery: { count: 0, inUse: 0 },
      inputs: { count: 0 },
      crops: { count: 0 },
      fieldArea: 0,
      totalRevenue: 0
    },
    
    // 作物数据
    crops: [],
    
    // 最新动态
    recentActivities: [],
    
    // 页面状态
    loading: true,
    isEmpty: false
  },

  // 页面加载
  onLoad: function (options) {
    console.log('Index page loaded with options:', options)
    this.initPage()
  },

  // 页面显示
  onShow: function () {
    console.log('Index page shown')
    this.refreshData()
  },

  // 页面隐藏
  onHide: function () {
    console.log('Index page hidden')
  },

  // 页面卸载
  onUnload: function () {
    console.log('Index page unloaded')
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    console.log('Pull down refresh')
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 上拉加载更多
  onReachBottom: function () {
    console.log('Reach bottom')
    // 首页不需要分页加载
  },

  // 分享
  onShareAppMessage: function () {
    return {
      title: '智慧农场管理系统',
      path: '/pages/index/index',
      imageUrl: '/assets/images/share-index.png'
    }
  },

  // 初始化页面
  initPage: function () {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '智慧农场'
    })
    
    // 订阅store变化
    this.subscribeStore()
    
    // 加载数据
    this.loadData()
  },

  // 订阅store变化
  subscribeStore: function () {
    // 监听农场数据变化
    store.subscribe('farm', (farmData) => {
      if (farmData.current) {
        this.setData({
          farmName: farmData.current.name || '金桥农场',
          location: farmData.current.location || this.data.location
        })
      }
    })
    
    // 监听天气数据变化
    store.subscribe('weather', (weatherData) => {
      if (weatherData.current) {
        this.setData({
          weather: {
            ...this.data.weather,
            ...weatherData.current
          }
        })
      }
    })
  },

  // 加载数据
  loadData: function () {
    this.setData({ loading: true })
    
    Promise.all([
      this.loadFarmStats(),
      this.loadCropData(),
      this.loadRecentActivities()
    ]).then(() => {
      this.setData({ 
        loading: false,
        isEmpty: this.checkIfEmpty()
      })
    }).catch((error) => {
      console.error('Load data failed:', error)
      this.setData({ loading: false })
      showToast('数据加载失败')
    })
  },

  // 刷新数据
  refreshData: function () {
    return this.loadData()
  },

  // 加载农场统计数据
  loadFarmStats: function () {
    return api.farm.getFarmStats().then((response) => {
      const stats = response.data || {}
      this.setData({
        farmStats: {
          machinery: stats.machinery || { count: 16, inUse: 15 },
          inputs: stats.inputs || { count: 54 },
          crops: stats.crops || { count: 3 },
          fieldArea: stats.fieldArea || 383,
          totalRevenue: stats.totalRevenue || 9930.1
        }
      })
      
      // 更新store
      store.setState({
        farm: {
          stats: this.data.farmStats
        }
      })
    })
  },

  // 加载作物数据
  loadCropData: function () {
    return api.crop.getCropList().then((response) => {
      const crops = response.data || []
      this.setData({
        crops: crops.map(crop => ({
          ...crop,
          progress: crop.progress || Math.random() * 0.8 + 0.2 // 模拟进度
        }))
      })
      
      // 更新store
      store.setState({
        crops: {
          list: this.data.crops
        }
      })
    })
  },

  // 加载最新动态
  loadRecentActivities: function () {
    // 这里可以加载最新的任务、消息等动态信息
    return Promise.resolve().then(() => {
      const activities = [
        {
          id: 1,
          title: '小麦播种任务完成',
          description: '小河口村1组播种任务已完成60%',
          time: '2小时前'
        },
        {
          id: 2,
          title: '设备维护提醒',
          description: '拖拉机001需要进行定期维护',
          time: '4小时前'
        }
      ]
      
      this.setData({
        recentActivities: activities
      })
    })
  },

  // 检查是否为空
  checkIfEmpty: function () {
    return this.data.crops.length === 0 && 
           this.data.farmStats.machinery.count === 0
  },

  // 导航到任务页面
  navigateToTasks: function () {
    wx.switchTab({
      url: '/pages/tasks/tasks'
    })
  },

  // 导航到设备页面
  navigateToDevices: function () {
    wx.navigateTo({
      url: '/pages/devices/devices'
    })
  },

  // 导航到作物页面
  navigateToCrops: function () {
    wx.navigateTo({
      url: '/pages/crops/crops'
    })
  },

  // 导航到报表页面
  navigateToReports: function () {
    wx.navigateTo({
      url: '/pages/reports/reports'
    })
  },

  // 点击天气卡片
  onWeatherTap: function () {
    wx.navigateTo({
      url: '/pages/weather/weather'
    })
  },

  // 点击统计卡片
  onStatsTap: function (e) {
    const type = e.currentTarget.dataset.type
    switch (type) {
      case 'machinery':
        this.navigateToDevices()
        break
      case 'inputs':
        wx.navigateTo({
          url: '/pages/inputs/inputs'
        })
        break
      case 'crops':
        this.navigateToCrops()
        break
      default:
        break
    }
  },

  // 点击作物项
  onCropTap: function (e) {
    const crop = e.currentTarget.dataset.crop
    wx.navigateTo({
      url: `/pages/crop-detail/crop-detail?id=${crop.id}&name=${crop.name}`
    })
  },

  // 点击最新动态
  onActivityTap: function (e) {
    const activity = e.currentTarget.dataset.activity
    // 根据动态类型跳转到相应页面
    if (activity.type === 'task') {
      wx.navigateTo({
        url: `/pages/task-detail/task-detail?id=${activity.relatedId}`
      })
    }
  }
})
