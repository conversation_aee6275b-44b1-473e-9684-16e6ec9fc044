// 加载组件
Component({
  // 组件属性
  properties: {
    // 是否显示
    show: {
      type: Boolean,
      value: false
    },
    
    // 加载文本
    text: {
      type: String,
      value: '加载中...'
    },
    
    // 是否显示遮罩
    mask: {
      type: Boolean,
      value: true
    },
    
    // 点击遮罩是否关闭
    maskClosable: {
      type: Boolean,
      value: false
    }
  },

  // 组件数据
  data: {
    
  },

  // 组件方法
  methods: {
    // 点击遮罩
    onBackdropTap: function() {
      if (this.properties.maskClosable) {
        this.triggerEvent('close')
      }
    },
    
    // 显示加载
    show: function(text) {
      this.setData({
        show: true,
        text: text || this.properties.text
      })
    },
    
    // 隐藏加载
    hide: function() {
      this.setData({
        show: false
      })
    }
  },

  // 组件生命周期
  lifetimes: {
    attached: function() {
      console.log('Loading component attached')
    },
    
    detached: function() {
      console.log('Loading component detached')
    }
  }
})
