// 工作台页面
import api from '../../api/index.js'
import store from '../../utils/store.js'
import { showToast } from '../../utils/common.js'

Page({
  data: {
    // 导航栏高度
    navBarHeight: 0,

    // 统计数据
    stats: {
      machinery: {
        total: 16,
        increase: 2
      },
      tasks: {
        completed: 28,
        increase: 5
      },
      area: {
        total: 1250,
        increase: 180
      }
    },
    
    // 页面状态
    loading: false
  },

  // 页面加载
  onLoad: function (options) {
    console.log('Workspace page loaded')
    this.initPage()
  },

  // 页面显示
  onShow: function () {
    console.log('Workspace page shown')
    this.refreshStats()

    // 更新 tabBar 选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().updateSelected(1) // 工作台页面索引为1
    }
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.refreshStats().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 分享
  onShareAppMessage: function () {
    return {
      title: '智慧农场工作台',
      path: '/pages/workspace/workspace'
    }
  },

  // 初始化页面
  initPage: function () {
    // 订阅store变化
    this.subscribeStore()

    // 加载统计数据
    this.loadStats()
  },

  // 导航栏高度回调
  onNavBarHeight: function(e) {
    this.setData({
      navBarHeight: e.detail.navBarHeight
    })
  },

  // 订阅store变化
  subscribeStore: function () {
    store.subscribe('farm.stats', (stats) => {
      if (stats) {
        this.updateStatsFromStore(stats)
      }
    })
  },

  // 从store更新统计数据
  updateStatsFromStore: function (farmStats) {
    this.setData({
      'stats.machinery.total': farmStats.machinery?.count || 16,
      'stats.area.total': farmStats.fieldArea || 1250
    })
  },

  // 加载统计数据
  loadStats: function () {
    this.setData({ loading: true })
    
    // 这里可以调用API获取最新的统计数据
    // 目前使用模拟数据
    return Promise.resolve().then(() => {
      // 模拟数据更新
      const stats = {
        machinery: {
          total: 16,
          increase: 2
        },
        tasks: {
          completed: 28,
          increase: 5
        },
        area: {
          total: 1250,
          increase: 180
        }
      }
      
      this.setData({ 
        stats,
        loading: false 
      })
    }).catch((error) => {
      console.error('Load stats failed:', error)
      this.setData({ loading: false })
      showToast('数据加载失败')
    })
  },

  // 刷新统计数据
  refreshStats: function () {
    return this.loadStats()
  },

  // 通用导航方法
  navigateTo: function (e) {
    const url = e.currentTarget.dataset.url
    if (!url) {
      showToast('功能开发中')
      return
    }
    
    // 判断是否为tabBar页面
    const tabBarPages = [
      '/pages/index/index',
      '/pages/tasks/tasks',
      '/pages/workspace/workspace',
      '/pages/messages/messages',
      '/pages/profile/profile'
    ]
    
    if (tabBarPages.includes(url)) {
      wx.switchTab({ url })
    } else {
      wx.navigateTo({ 
        url,
        fail: () => {
          showToast('页面不存在或开发中')
        }
      })
    }
  },

  // 快捷操作
  onQuickAction: function (e) {
    const action = e.currentTarget.dataset.action
    
    switch (action) {
      case 'scan':
        this.scanCode()
        break
      case 'location':
        this.getCurrentLocation()
        break
      case 'camera':
        this.takePhoto()
        break
      case 'voice':
        this.startVoiceRecord()
        break
      default:
        showToast('功能开发中')
        break
    }
  },

  // 扫码功能
  scanCode: function () {
    wx.scanCode({
      success: (res) => {
        console.log('Scan result:', res)
        showToast('扫码成功')
        // 处理扫码结果
        this.handleScanResult(res.result)
      },
      fail: () => {
        showToast('扫码失败')
      }
    })
  },

  // 处理扫码结果
  handleScanResult: function (result) {
    // 根据扫码结果跳转到相应页面
    if (result.startsWith('http')) {
      // 网页链接
      wx.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(result)}`
      })
    } else {
      // 其他类型的码
      showToast(`扫码结果: ${result}`)
    }
  },

  // 获取当前位置
  getCurrentLocation: function () {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        console.log('Location:', res)
        showToast('定位成功')
        
        // 可以跳转到地图页面显示位置
        wx.navigateTo({
          url: `/pages/map/map?latitude=${res.latitude}&longitude=${res.longitude}`
        })
      },
      fail: () => {
        showToast('定位失败，请检查定位权限')
      }
    })
  },

  // 拍照功能
  takePhoto: function () {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera'],
      success: (res) => {
        console.log('Photo taken:', res)
        const tempFilePath = res.tempFilePaths[0]
        
        // 可以跳转到图片处理页面
        wx.navigateTo({
          url: `/pages/photo-process/photo-process?image=${encodeURIComponent(tempFilePath)}`
        })
      },
      fail: () => {
        showToast('拍照失败')
      }
    })
  },

  // 语音录制
  startVoiceRecord: function () {
    wx.startRecord({
      success: (res) => {
        console.log('Voice recorded:', res)
        showToast('录音成功')
        
        // 处理录音文件
        this.handleVoiceRecord(res.tempFilePath)
      },
      fail: () => {
        showToast('录音失败')
      }
    })
  },

  // 处理录音文件
  handleVoiceRecord: function (filePath) {
    // 可以上传录音文件或进行语音识别
    showToast('录音处理中...')
  }
})
