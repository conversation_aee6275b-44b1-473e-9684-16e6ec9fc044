// 任务页面
import api from '../../api/index.js'
import store from '../../utils/store.js'
import { showToast, showConfirm, debounce } from '../../utils/common.js'

Page({
  data: {
    // 导航栏高度
    navBarHeight: 88, // 设置默认值，避免初始渲染时内容被遮挡

    // 搜索关键词
    searchKeyword: '',

    // 任务列表
    taskList: [],

    // 页面状态
    loading: true,
    refreshing: false,

    // 分页参数
    page: 1,
    pageSize: 20,
    hasMore: true
  },

  // 页面加载
  onLoad: function (options) {
    console.log('Tasks page loaded')

    // 立即计算导航栏高度
    const systemInfo = wx.getSystemInfoSync()
    const statusBarHeight = systemInfo.statusBarHeight || 20
    const titleBarHeight = 44
    const navBarHeight = statusBarHeight + titleBarHeight

    this.setData({
      navBarHeight: navBarHeight
    })

    this.initPage()
  },

  // 页面显示
  onShow: function () {
    console.log('Tasks page shown')
    this.refreshData()

    // 更新 tabBar 选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().updateSelected(0) // 任务页面索引为0
    }
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 上拉加载更多
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreData()
    }
  },

  // 分享
  onShareAppMessage: function () {
    return {
      title: '农场任务管理',
      path: '/pages/tasks/tasks'
    }
  },

  // 初始化页面
  initPage: function () {
    // 订阅store变化
    this.subscribeStore()

    // 加载数据
    this.loadData()
  },

  // 导航栏高度回调
  onNavBarHeight: function(e) {
    this.setData({
      navBarHeight: e.detail.navBarHeight
    })
  },

  // 订阅store变化
  subscribeStore: function () {
    store.subscribe('tasks', (tasksData) => {
      if (tasksData.list) {
        this.setData({
          taskList: tasksData.list
        })
      }
    })
  },

  // 加载数据
  loadData: function () {
    this.setData({ 
      loading: true,
      page: 1,
      hasMore: true
    })
    
    const params = {
      page: 1,
      pageSize: this.data.pageSize,
      keyword: this.data.searchKeyword
    }
    
    return api.task.getTaskList(params).then((response) => {
      const tasks = response.data || []
      this.setData({
        taskList: tasks,
        loading: false,
        hasMore: tasks.length >= this.data.pageSize
      })
      
      // 更新store
      store.setState({
        tasks: {
          list: tasks
        }
      })
    }).catch((error) => {
      console.error('Load tasks failed:', error)
      this.setData({ loading: false })
      showToast('加载失败')
    })
  },

  // 刷新数据
  refreshData: function () {
    return this.loadData()
  },

  // 加载更多数据
  loadMoreData: function () {
    const nextPage = this.data.page + 1
    const params = {
      page: nextPage,
      pageSize: this.data.pageSize,
      keyword: this.data.searchKeyword
    }
    
    return api.task.getTaskList(params).then((response) => {
      const newTasks = response.data || []
      const allTasks = [...this.data.taskList, ...newTasks]
      
      this.setData({
        taskList: allTasks,
        page: nextPage,
        hasMore: newTasks.length >= this.data.pageSize
      })
      
      // 更新store
      store.setState({
        tasks: {
          list: allTasks
        }
      })
    }).catch((error) => {
      console.error('Load more tasks failed:', error)
      showToast('加载更多失败')
    })
  },

  // 搜索输入
  onSearchInput: debounce(function (e) {
    const keyword = e.detail.value.trim()
    this.setData({ searchKeyword: keyword })
    
    if (keyword !== this.data.searchKeyword) {
      this.loadData()
    }
  }, 500),

  // 搜索确认
  onSearchConfirm: function (e) {
    const keyword = e.detail.value.trim()
    this.setData({ searchKeyword: keyword })
    this.loadData()
  },

  // 清除搜索
  onSearchClear: function () {
    this.setData({ searchKeyword: '' })
    this.loadData()
  },

  // 点击任务
  onTaskTap: function (e) {
    const task = e.currentTarget.dataset.task
    wx.navigateTo({
      url: `/pages/task-detail/task-detail?id=${task.id}`
    })
  },

  // 开始任务
  onStartTask: function (e) {
    e.stopPropagation()
    const taskId = e.currentTarget.dataset.id
    
    showConfirm('确定要开始这个任务吗？').then((confirmed) => {
      if (confirmed) {
        this.updateTaskStatus(taskId, 'progress')
      }
    })
  },

  // 完成任务
  onCompleteTask: function (e) {
    e.stopPropagation()
    const taskId = e.currentTarget.dataset.id
    
    showConfirm('确定要完成这个任务吗？').then((confirmed) => {
      if (confirmed) {
        this.updateTaskStatus(taskId, 'completed')
      }
    })
  },

  // 查看任务
  onViewTask: function (e) {
    e.stopPropagation()
    const taskId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/task-detail/task-detail?id=${taskId}`
    })
  },

  // 巡田打卡
  onPatrolCheckIn: function (e) {
    e.stopPropagation()
    const taskId = e.currentTarget.dataset.id
    
    // 获取位置信息
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        // 跳转到打卡页面
        wx.navigateTo({
          url: `/pages/check-in/check-in?taskId=${taskId}&latitude=${res.latitude}&longitude=${res.longitude}`
        })
      },
      fail: () => {
        showToast('获取位置失败，请检查定位权限')
      }
    })
  },

  // 更新任务状态
  updateTaskStatus: function (taskId, status) {
    const updateData = { status }
    
    // 如果是完成任务，设置进度为100%
    if (status === 'completed') {
      updateData.progress = 100
    } else if (status === 'progress') {
      updateData.progress = 10 // 开始时设置为10%
    }
    
    return api.task.updateTask(taskId, updateData).then(() => {
      // 更新本地数据
      const taskList = this.data.taskList.map(task => {
        if (task.id === taskId) {
          return { ...task, ...updateData }
        }
        return task
      })
      
      this.setData({ taskList })
      
      // 更新store
      store.setState({
        tasks: {
          list: taskList
        }
      })
      
      const statusText = status === 'progress' ? '已开始' : '已完成'
      showToast(`任务${statusText}`)
    }).catch((error) => {
      console.error('Update task status failed:', error)
      showToast('操作失败')
    })
  },

  // 创建任务
  onCreateTask: function () {
    wx.navigateTo({
      url: '/pages/task-create/task-create'
    })
  },

  // 获取进度条颜色
  getProgressColor: function (progress, status) {
    if (status === 'completed') {
      return '#4CAF50' // 绿色
    } else if (status === 'progress') {
      if (progress >= 80) {
        return '#4CAF50' // 绿色
      } else if (progress >= 50) {
        return '#FF9800' // 橙色
      } else {
        return '#2196F3' // 蓝色
      }
    } else {
      return '#E0E0E0' // 灰色
    }
  }
})
