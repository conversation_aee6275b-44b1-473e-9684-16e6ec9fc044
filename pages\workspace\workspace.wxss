/* 工作台页面样式 */

.container {
  background-color: var(--bg-color);
  min-height: 100vh;
}

/* 区块样式 */
.section {
  margin-bottom: var(--spacing-lg);
}

.section-header {
  padding: var(--spacing-base) var(--spacing-base) var(--spacing-sm);
  background-color: var(--bg-white);
  border-bottom: 1rpx solid var(--border-light);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
}

/* 工作台网格 */
.workspace-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-base);
  padding: var(--spacing-base);
  background-color: var(--bg-white);
}

.workspace-item {
  background-color: var(--bg-white);
  border: 1rpx solid var(--border-light);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  transition: all var(--transition-fast);
  min-height: 160rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.workspace-item:active {
  transform: scale(0.98);
  background-color: var(--bg-gray);
  border-color: var(--primary-light);
}

.workspace-icon {
  font-size: 64rpx;
  margin-bottom: var(--spacing-sm);
  opacity: 0.8;
  display: block;
  text-align: center;
}

.workspace-title {
  font-size: var(--font-size-base);
  color: var(--text-color);
  font-weight: var(--font-weight-medium);
}

/* 快捷功能 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-base);
  padding: var(--spacing-base);
  background-color: var(--bg-white);
}

.quick-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-base);
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-fast);
  min-height: 120rpx;
  justify-content: center;
}

.quick-action-item:active {
  background-color: var(--bg-gray);
  transform: scale(0.95);
}

.quick-action-icon {
  font-size: 48rpx;
  margin-bottom: var(--spacing-xs);
  opacity: 0.7;
  display: block;
  text-align: center;
}

.quick-action-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
}

/* 统计卡片 */
.stats-cards {
  padding: var(--spacing-base);
  background-color: var(--bg-white);
}

.stats-card {
  display: flex;
  align-items: center;
  padding: var(--spacing-base);
  margin-bottom: var(--spacing-sm);
  background-color: var(--bg-gray);
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-fast);
}

.stats-card:last-child {
  margin-bottom: 0;
}

.stats-card:active {
  background-color: var(--border-light);
  transform: scale(0.98);
}

.stats-icon {
  font-size: 48rpx;
  margin-right: var(--spacing-base);
  width: 80rpx;
  text-align: center;
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  line-height: 1.2;
}

.stats-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}

.stats-trend {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-base);
}

.stats-trend-up {
  background-color: #E8F5E8;
  color: var(--success-color);
}

.stats-trend-down {
  background-color: #FFEBEE;
  color: var(--danger-color);
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
}

.loading-text {
  margin-left: var(--spacing-sm);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .workspace-grid {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
  }
  
  .workspace-item {
    padding: var(--spacing-base);
    min-height: 140rpx;
  }
  
  .workspace-icon {
    width: 56rpx;
    height: 56rpx;
  }
  
  .quick-actions {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
  }
  
  .quick-action-item {
    padding: var(--spacing-sm);
    min-height: 100rpx;
  }
  
  .quick-action-icon {
    width: 40rpx;
    height: 40rpx;
  }
  
  .stats-card {
    padding: var(--spacing-sm);
  }
  
  .stats-icon {
    font-size: 40rpx;
    width: 64rpx;
  }
  
  .stats-value {
    font-size: var(--font-size-lg);
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .workspace-item {
    background-color: var(--bg-dark);
    border-color: #444;
  }
  
  .workspace-item:active {
    background-color: #333;
  }
  
  .stats-card {
    background-color: var(--bg-dark);
  }
  
  .stats-card:active {
    background-color: #333;
  }
}
