<!-- 自定义 tabBar -->
<view class="tab-bar">
  <view wx:for="{{list}}" wx:key="index" class="tab-bar-item {{selected === index ? 'selected' : ''}}" bindtap="switchTab" data-index="{{index}}">
    <!-- 图标 -->
    <view class="tab-bar-icon">
      <text class="iconfont {{selected === index ? item.selectedIcon : item.icon}}"></text>
    </view>
    
    <!-- 文字 -->
    <view class="tab-bar-text">{{item.text}}</view>
    
    <!-- 红点提示 -->
    <view wx:if="{{item.badge}}" class="tab-bar-badge">{{item.badge}}</view>
  </view>
</view>
