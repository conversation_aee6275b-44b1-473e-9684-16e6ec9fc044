/* 加载组件样式 */

.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity var(--transition-base);
}

.loading-container.show {
  opacity: 1;
  pointer-events: auto;
}

.loading-container.hide {
  opacity: 0;
  pointer-events: none;
}

.loading-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.loading-content {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 200rpx;
  box-shadow: var(--shadow-lg);
}

/* 加载动画 */
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-base);
}

.spinner-dot {
  width: 16rpx;
  height: 16rpx;
  background-color: var(--primary-color);
  border-radius: 50%;
  margin: 0 4rpx;
  animation: spinner-bounce 1.4s ease-in-out infinite both;
}

.spinner-dot.dot1 {
  animation-delay: -0.32s;
}

.spinner-dot.dot2 {
  animation-delay: -0.16s;
}

.spinner-dot.dot3 {
  animation-delay: 0s;
}

@keyframes spinner-bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 加载文本 */
.loading-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
}
