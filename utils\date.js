// 日期处理工具函数

/**
 * 格式化日期
 * @param {Date|string|number} date 日期
 * @param {string} format 格式化字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  const hour = d.getHours()
  const minute = d.getMinutes()
  const second = d.getSeconds()
  const weekDay = d.getDay()
  
  const weekDays = ['日', '一', '二', '三', '四', '五', '六']
  const weekDaysFull = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  
  const formatMap = {
    'YYYY': year,
    'YY': String(year).slice(-2),
    'MM': String(month).padStart(2, '0'),
    'M': month,
    'DD': String(day).padStart(2, '0'),
    'D': day,
    'HH': String(hour).padStart(2, '0'),
    'H': hour,
    'mm': String(minute).padStart(2, '0'),
    'm': minute,
    'ss': String(second).padStart(2, '0'),
    's': second,
    'w': weekDays[weekDay],
    'W': weekDaysFull[weekDay],
    '星期': weekDaysFull[weekDay]
  }
  
  let result = format
  Object.keys(formatMap).forEach(key => {
    result = result.replace(new RegExp(key, 'g'), formatMap[key])
  })
  
  return result
}

/**
 * 获取相对时间
 * @param {Date|string|number} date 日期
 */
export function getRelativeTime(date) {
  if (!date) return ''
  
  const now = new Date()
  const target = new Date(date)
  const diff = now.getTime() - target.getTime()
  
  if (diff < 0) return '未来时间'
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return Math.floor(diff / minute) + '分钟前'
  } else if (diff < day) {
    return Math.floor(diff / hour) + '小时前'
  } else if (diff < week) {
    return Math.floor(diff / day) + '天前'
  } else if (diff < month) {
    return Math.floor(diff / week) + '周前'
  } else if (diff < year) {
    return Math.floor(diff / month) + '个月前'
  } else {
    return Math.floor(diff / year) + '年前'
  }
}

/**
 * 判断是否为今天
 * @param {Date|string|number} date 日期
 */
export function isToday(date) {
  if (!date) return false
  
  const today = new Date()
  const target = new Date(date)
  
  return today.getFullYear() === target.getFullYear() &&
         today.getMonth() === target.getMonth() &&
         today.getDate() === target.getDate()
}

/**
 * 判断是否为昨天
 * @param {Date|string|number} date 日期
 */
export function isYesterday(date) {
  if (!date) return false
  
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  const target = new Date(date)
  
  return yesterday.getFullYear() === target.getFullYear() &&
         yesterday.getMonth() === target.getMonth() &&
         yesterday.getDate() === target.getDate()
}

/**
 * 判断是否为本周
 * @param {Date|string|number} date 日期
 */
export function isThisWeek(date) {
  if (!date) return false
  
  const now = new Date()
  const target = new Date(date)
  
  // 获取本周一的日期
  const monday = new Date(now)
  monday.setDate(now.getDate() - now.getDay() + 1)
  monday.setHours(0, 0, 0, 0)
  
  // 获取本周日的日期
  const sunday = new Date(monday)
  sunday.setDate(monday.getDate() + 6)
  sunday.setHours(23, 59, 59, 999)
  
  return target >= monday && target <= sunday
}

/**
 * 判断是否为本月
 * @param {Date|string|number} date 日期
 */
export function isThisMonth(date) {
  if (!date) return false
  
  const now = new Date()
  const target = new Date(date)
  
  return now.getFullYear() === target.getFullYear() &&
         now.getMonth() === target.getMonth()
}

/**
 * 判断是否为本年
 * @param {Date|string|number} date 日期
 */
export function isThisYear(date) {
  if (!date) return false
  
  const now = new Date()
  const target = new Date(date)
  
  return now.getFullYear() === target.getFullYear()
}

/**
 * 获取月份天数
 * @param {number} year 年份
 * @param {number} month 月份（1-12）
 */
export function getDaysInMonth(year, month) {
  return new Date(year, month, 0).getDate()
}

/**
 * 获取日期范围
 * @param {Date|string|number} startDate 开始日期
 * @param {Date|string|number} endDate 结束日期
 */
export function getDateRange(startDate, endDate) {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const dates = []
  
  const current = new Date(start)
  while (current <= end) {
    dates.push(new Date(current))
    current.setDate(current.getDate() + 1)
  }
  
  return dates
}

/**
 * 计算日期差
 * @param {Date|string|number} date1 日期1
 * @param {Date|string|number} date2 日期2
 * @param {string} unit 单位 days|hours|minutes|seconds
 */
export function dateDiff(date1, date2, unit = 'days') {
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  const diff = Math.abs(d2.getTime() - d1.getTime())
  
  switch (unit) {
    case 'seconds':
      return Math.floor(diff / 1000)
    case 'minutes':
      return Math.floor(diff / (1000 * 60))
    case 'hours':
      return Math.floor(diff / (1000 * 60 * 60))
    case 'days':
      return Math.floor(diff / (1000 * 60 * 60 * 24))
    default:
      return diff
  }
}

/**
 * 添加日期
 * @param {Date|string|number} date 日期
 * @param {number} amount 数量
 * @param {string} unit 单位 years|months|days|hours|minutes|seconds
 */
export function addDate(date, amount, unit = 'days') {
  const result = new Date(date)
  
  switch (unit) {
    case 'years':
      result.setFullYear(result.getFullYear() + amount)
      break
    case 'months':
      result.setMonth(result.getMonth() + amount)
      break
    case 'days':
      result.setDate(result.getDate() + amount)
      break
    case 'hours':
      result.setHours(result.getHours() + amount)
      break
    case 'minutes':
      result.setMinutes(result.getMinutes() + amount)
      break
    case 'seconds':
      result.setSeconds(result.getSeconds() + amount)
      break
  }
  
  return result
}

/**
 * 获取季度
 * @param {Date|string|number} date 日期
 */
export function getQuarter(date) {
  const d = new Date(date)
  const month = d.getMonth() + 1
  return Math.ceil(month / 3)
}

/**
 * 获取周数
 * @param {Date|string|number} date 日期
 */
export function getWeekOfYear(date) {
  const d = new Date(date)
  const yearStart = new Date(d.getFullYear(), 0, 1)
  const today = new Date(d.getFullYear(), d.getMonth(), d.getDate())
  const dayOfYear = ((today - yearStart + 86400000) / 86400000)
  return Math.ceil(dayOfYear / 7)
}

/**
 * 解析日期字符串
 * @param {string} dateString 日期字符串
 */
export function parseDate(dateString) {
  if (!dateString) return null
  
  // 处理常见的日期格式
  const formats = [
    /^\d{4}-\d{2}-\d{2}$/,           // YYYY-MM-DD
    /^\d{4}\/\d{2}\/\d{2}$/,         // YYYY/MM/DD
    /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/, // YYYY-MM-DD HH:mm:ss
    /^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}:\d{2}$/, // YYYY/MM/DD HH:mm:ss
  ]
  
  for (const format of formats) {
    if (format.test(dateString)) {
      const date = new Date(dateString)
      if (!isNaN(date.getTime())) {
        return date
      }
    }
  }
  
  return null
}
