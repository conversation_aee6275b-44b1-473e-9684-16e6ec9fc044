// 引入全局配置和工具
import config from './config/index.js'
import store from './utils/store.js'
import { formatDate } from './utils/date.js'

App({
  // 全局数据
  globalData: {
    userInfo: null,
    systemInfo: null,
    config: config,
    store: store
  },

  // 应用启动
  onLaunch: function (options) {
    console.log('App Launch', options)
    this.initApp()
  },

  // 应用显示
  onShow: function (options) {
    console.log('App Show', options)
  },

  // 应用隐藏
  onHide: function () {
    console.log('App Hide')
  },

  // 应用错误
  onError: function (msg) {
    console.error('App Error', msg)
    // 可以在这里上报错误日志
  },

  // 初始化应用
  initApp: function () {
    // 获取系统信息
    this.getSystemInfo()
    
    // 初始化全局状态
    this.initGlobalState()
    
    // 检查更新
    this.checkUpdate()
  },

  // 获取系统信息
  getSystemInfo: function () {
    const that = this
    wx.getSystemInfo({
      success: function (res) {
        that.globalData.systemInfo = res
        console.log('系统信息:', res)
      }
    })
  },

  // 初始化全局状态
  initGlobalState: function () {
    // 初始化store
    store.init()
    
    // 设置默认数据
    store.setState({
      currentFarm: '金桥农场',
      location: '甘肃省酒泉市金塔县东坝镇',
      weather: {
        temperature: '25.1°C',
        condition: '小雨',
        humidity: '91%',
        date: formatDate(new Date(), 'YYYY/MM/DD 星期')
      }
    })
  },

  // 检查小程序更新
  checkUpdate: function () {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate(function (res) {
        console.log('检查更新:', res.hasUpdate)
      })
      
      updateManager.onUpdateReady(function () {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: function (res) {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })
      
      updateManager.onUpdateFailed(function () {
        console.error('新版本下载失败')
      })
    }
  },

  // 全局方法：获取用户信息
  getUserInfo: function (callback) {
    const that = this
    if (this.globalData.userInfo) {
      typeof callback === 'function' && callback(this.globalData.userInfo)
    } else {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: function (res) {
          that.globalData.userInfo = res.userInfo
          typeof callback === 'function' && callback(that.globalData.userInfo)
        },
        fail: function () {
          console.log('获取用户信息失败')
        }
      })
    }
  },

  // 全局方法：显示加载提示
  showLoading: function (title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    })
  },

  // 全局方法：隐藏加载提示
  hideLoading: function () {
    wx.hideLoading()
  },

  // 全局方法：显示提示信息
  showToast: function (title, icon = 'none', duration = 2000) {
    wx.showToast({
      title: title,
      icon: icon,
      duration: duration
    })
  }
})
