---
description: "CMAF 框架的核心架构、设计原则与模块化规范。用于指导 Cursor 进行功能开发、重构和文件组织。"
globs:
  # 应用于项目级配置文件和核心架构目录
  - './app.json'
  - './project.config.json'
  - './pages/**/*'
  - './components/**/*'
  - './services/**/*'
  - './store/**/*'
  - './utils/**/*'
alwaysApply: true
---

# CMAF 框架架构与设计准则

## 1. 核心设计原则 (必须遵守)

- **轻量化**: 严禁引入除 `Vant Weapp` 和 `mobx-miniprogram` 之外的任何重型框架或库。所有功能实现应优先考虑使用小程序原生 API。
- **性能优先**: 所有代码生成和重构建议，都必须将性能影响作为首要考量。特别是关于 `setData` 的调用、图片加载和分包策略。
- **模块化**: 严格遵循高内聚、低耦合原则。不同业务模块的代码必须物理隔离。
- **易用性**: 封装的函数和组件，API 设计必须简洁、直观。

## 2. 文件组织与创建规则

- **创建页面**:
    - 当被要求创建新页面时，优先询问该页面应属于主包还是分包。
    - 主包 (`/pages`) 只允许存放 TabBar 页面和启动页。
    - 其他业务页面必须创建在相应的分包 (`/package*`) 中。如果分包不存在，应主动提议创建。
- **创建组件**:
    - **全局组件**: 如果一个组件将在多个分包或页面中使用（如自定义导航栏、授权弹窗），必须在根目录 `/components` 下创建。
    - **业务组件**: 如果一个组件与特定业务高度耦合（如“订单卡片”），必须在所属分包的 `components` 文件夹内创建。
- **创建 API 服务**:
    - 绝不允许在页面的 JS 文件中直接调用 `wx.request`。
    - 所有对后端的请求必须在 `/services` 目录下创建或修改。按业务模块（如 `user.js`, `product.js`）组织文件。
    - 所有请求必须通过 `/services/request.js` 封装的函数发起。
- **创建状态管理**:
    - 仅当数据需要跨多个页面共享时（如用户信息、购物车），才可以在 `/store` 目录中创建或修改 MobX store。
    - 页面内部的状态，必须使用页面自身的 `data` 和 `setData` 进行管理。

## 3. 架构决策规则

- **状态管理决策**:
    - **提问**: "这个数据是否需要在多个页面之间共享或保持同步？"
    - **回答为“是”**: 使用 `/store` 中的全局 MobX store。
    - **回答为“否”**: 使用当前页面的 `this.data`。
- **分包决策**:
    - **提问**: "这个功能是小程序的核心入口功能吗？（例如首页、我的）"
    - **回答为“是”**: 放入主包 `/pages`。
    - **回答为“否”**: 询问其业务归属，并放入对应的分包 `/package*`。
- **组件化决策**:
    - 当发现 WXML 中有超过 20 行的重复结构，或者某段 UI 逻辑可以被复用时，应主动提议将其抽取为独立组件。