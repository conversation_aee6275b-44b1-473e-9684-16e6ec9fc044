# 使用指南

## 框架概述

这是一个功能完整的微信小程序敏捷开发框架，专为智慧农场管理系统设计，包含了完整的项目结构、样式系统、状态管理、网络请求封装等功能。

## 主要特性

### 1. 全局状态管理 (utils/store.js)

提供了轻量级的状态管理解决方案：

```javascript
// 设置状态
store.setState({
  user: {
    name: '张三',
    isLogin: true
  }
})

// 获取状态
const userName = store.getState('user.name')

// 订阅状态变化
const unsubscribe = store.subscribe('user', (userData) => {
  console.log('用户数据更新:', userData)
})

// 取消订阅
unsubscribe()
```

### 2. 网络请求封装 (utils/request.js)

统一的网络请求处理：

```javascript
import request from '../utils/request.js'

// GET请求
request.get('/api/users', { page: 1 })
  .then(data => console.log(data))
  .catch(error => console.error(error))

// POST请求
request.post('/api/users', { name: '张三' })
  .then(data => console.log(data))

// 文件上传
request.upload('/api/upload', filePath, {
  name: 'image',
  formData: { type: 'avatar' }
})
```

### 3. API管理 (api/index.js)

统一的API接口管理：

```javascript
import api from '../api/index.js'

// 用户相关
api.user.login({ username, password })
api.user.getUserInfo()

// 任务相关
api.task.getTaskList({ page: 1 })
api.task.createTask(taskData)
api.task.updateTask(id, updateData)
```

### 4. 工具函数库 (utils/common.js)

丰富的工具函数：

```javascript
import { 
  debounce, 
  throttle, 
  formatNumber, 
  showToast, 
  showConfirm 
} from '../utils/common.js'

// 防抖
const debouncedSearch = debounce(searchFunction, 500)

// 节流
const throttledScroll = throttle(scrollFunction, 100)

// 数字格式化
const formatted = formatNumber(1234567) // "1.23M"

// 提示框
showToast('操作成功', 'success')
showConfirm('确定删除吗？').then(confirmed => {
  if (confirmed) {
    // 执行删除
  }
})
```

### 5. 日期处理 (utils/date.js)

专业的日期处理工具：

```javascript
import { 
  formatDate, 
  getRelativeTime, 
  isToday, 
  dateDiff 
} from '../utils/date.js'

// 日期格式化
formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss')
formatDate(new Date(), 'YYYY年MM月DD日 星期')

// 相对时间
getRelativeTime(new Date()) // "刚刚", "5分钟前"

// 判断是否为今天
isToday(new Date()) // true

// 计算日期差
dateDiff(date1, date2, 'days') // 天数差
```

## 样式系统

### CSS变量 (styles/variables.wxss)

完整的CSS变量系统：

```css
/* 颜色变量 */
--primary-color: #4CAF50;
--success-color: #4CAF50;
--warning-color: #FF9800;
--danger-color: #F44336;

/* 尺寸变量 */
--font-size-xs: 20rpx;
--font-size-sm: 24rpx;
--font-size-base: 28rpx;
--font-size-lg: 32rpx;

/* 间距变量 */
--spacing-xs: 8rpx;
--spacing-sm: 16rpx;
--spacing-base: 24rpx;
--spacing-lg: 32rpx;
```

### 通用样式类 (styles/common.wxss)

大量通用样式类：

```html
<!-- 布局类 -->
<view class="flex flex-center flex-between">
  <text>左侧内容</text>
  <text>右侧内容</text>
</view>

<!-- 间距类 -->
<view class="p-3 m-2 mt-4 mb-1">内容</view>

<!-- 文本类 -->
<text class="text-center text-primary text-lg">标题</text>

<!-- 按钮类 -->
<view class="btn btn-primary btn-lg">按钮</view>

<!-- 卡片类 -->
<view class="card">
  <view class="card-header">
    <view class="card-title">标题</view>
  </view>
  <view class="card-body">内容</view>
</view>
```

## 页面开发

### 页面结构

每个页面包含四个文件：

```
pages/example/
├── example.wxml    # 页面结构
├── example.js      # 页面逻辑
├── example.wxss    # 页面样式
└── example.json    # 页面配置
```

### 页面模板

```javascript
// example.js
import api from '../../api/index.js'
import store from '../../utils/store.js'
import { showToast } from '../../utils/common.js'

Page({
  data: {
    loading: false,
    dataList: []
  },

  onLoad: function(options) {
    this.initPage()
  },

  onShow: function() {
    this.refreshData()
  },

  onPullDownRefresh: function() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  initPage: function() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '页面标题'
    })
    
    // 订阅store变化
    store.subscribe('dataKey', (data) => {
      this.setData({ dataList: data })
    })
    
    // 加载数据
    this.loadData()
  },

  loadData: function() {
    this.setData({ loading: true })
    
    return api.getData().then(response => {
      this.setData({
        dataList: response.data,
        loading: false
      })
    }).catch(error => {
      console.error('Load data failed:', error)
      this.setData({ loading: false })
      showToast('加载失败')
    })
  },

  refreshData: function() {
    return this.loadData()
  }
})
```

## 组件开发

### 组件结构

```
components/common/my-component/
├── my-component.wxml
├── my-component.js
├── my-component.wxss
└── my-component.json
```

### 组件模板

```javascript
// my-component.js
Component({
  properties: {
    title: {
      type: String,
      value: '默认标题'
    },
    data: {
      type: Array,
      value: []
    }
  },

  data: {
    internalData: ''
  },

  methods: {
    onItemTap: function(e) {
      const item = e.currentTarget.dataset.item
      this.triggerEvent('itemtap', { item })
    }
  },

  lifetimes: {
    attached: function() {
      console.log('Component attached')
    }
  }
})
```

### 使用组件

```json
// 页面配置文件
{
  "usingComponents": {
    "my-component": "/components/common/my-component/my-component"
  }
}
```

```html
<!-- 页面模板 -->
<my-component 
  title="组件标题" 
  data="{{dataList}}"
  bind:itemtap="onItemTap">
</my-component>
```

## 配置说明

### 全局配置 (config/index.js)

```javascript
const config = {
  // 环境配置
  env: 'development', // development, production
  
  // API配置
  api: {
    baseUrl: 'https://api.example.com',
    timeout: 10000,
    endpoints: {
      login: '/auth/login',
      userInfo: '/user/info'
    }
  },
  
  // 存储配置
  storage: {
    prefix: 'wxpro_',
    keys: {
      userInfo: 'user_info',
      token: 'access_token'
    }
  },
  
  // 功能开关
  features: {
    debug: true,
    errorReport: true
  }
}
```

### 页面配置

```json
{
  "navigationBarTitleText": "页面标题",
  "enablePullDownRefresh": true,
  "onReachBottomDistance": 50,
  "backgroundTextStyle": "dark"
}
```

## 最佳实践

### 1. 代码组织

- 保持文件结构清晰
- 使用有意义的命名
- 添加必要的注释
- 遵循统一的代码风格

### 2. 性能优化

- 合理使用setData，避免频繁更新
- 使用分页加载大量数据
- 图片懒加载和压缩
- 及时清理定时器和监听器

### 3. 用户体验

- 提供加载状态反馈
- 处理网络异常情况
- 保持界面响应流畅
- 适配不同屏幕尺寸

### 4. 错误处理

- 统一的错误处理机制
- 用户友好的错误提示
- 错误日志记录和上报
- 降级处理方案

## 常见问题

### Q: 如何添加新的API接口？

A: 在 `api/index.js` 中添加新的方法，使用统一的请求封装。

### Q: 如何自定义主题色彩？

A: 修改 `styles/variables.wxss` 中的CSS变量，或在 `config/index.js` 中配置主题。

### Q: 如何处理用户登录状态？

A: 使用全局状态管理，在 `store` 中维护用户信息和登录状态。

### Q: 如何实现页面间数据传递？

A: 可以通过URL参数、全局状态管理或事件总线实现。

### Q: 如何调试网络请求？

A: 在开发环境下，请求拦截器会自动打印请求和响应信息。

## 更新日志

### v1.0.0
- 初始版本发布
- 完整的框架结构
- 基础功能实现

---

更多详细信息请参考 [README.md](./README.md) 文件。
