/* 首页样式 */

.container {
  padding: var(--spacing-base);
  background-color: var(--bg-color);
  min-height: 100vh;
}

/* 天气卡片特殊样式 */
.weather-card {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: var(--text-white);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-base);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.weather-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.weather-location {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-sm);
  position: relative;
  z-index: 1;
}

.weather-location-icon {
  font-size: 28rpx;
  margin-right: var(--spacing-xs);
}

.weather-location-text {
  font-size: var(--font-size-base);
  opacity: 0.9;
}

.weather-main {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.weather-left {
  flex: 1;
}

.weather-temp {
  font-size: 72rpx;
  font-weight: var(--font-weight-bold);
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.weather-condition {
  font-size: var(--font-size-base);
  opacity: 0.8;
  margin-bottom: var(--spacing-xs);
}

.weather-date {
  font-size: var(--font-size-sm);
  opacity: 0.7;
}

.weather-right {
  display: flex;
  align-items: center;
}

.weather-farm {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  opacity: 0.9;
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-base);
}

.weather-farm-icon {
  font-size: 24rpx;
  margin-right: var(--spacing-xs);
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-base);
  margin-bottom: var(--spacing-base);
}

.stats-card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-base);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.stats-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-base);
}

.stats-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
}

.stats-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  line-height: 1.2;
}

.stats-unit {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  font-weight: var(--font-weight-normal);
  display: block;
  margin-top: var(--spacing-xs);
}

/* 数据卡片 */
.data-card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-base);
  margin-bottom: var(--spacing-base);
  box-shadow: var(--shadow-sm);
}

.data-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) 0;
}

.data-row:not(:last-child) {
  border-bottom: 1rpx solid var(--border-light);
}

.data-label {
  display: flex;
  align-items: center;
  font-size: var(--font-size-base);
  color: var(--text-color);
}

.data-icon {
  font-size: 28rpx;
  margin-right: var(--spacing-sm);
  opacity: 0.8;
}

.data-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
}

/* 作物项样式 */
.crop-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) 0;
  transition: background-color var(--transition-fast);
}

.crop-item:active {
  background-color: var(--bg-gray);
  border-radius: var(--border-radius-base);
  margin: 0 calc(-1 * var(--spacing-sm));
  padding: var(--spacing-sm);
}

.crop-name {
  font-size: var(--font-size-base);
  color: var(--text-color);
  min-width: 80rpx;
}

.crop-progress {
  flex: 1;
  margin: 0 var(--spacing-base);
}

.crop-area {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  min-width: 100rpx;
  text-align: right;
}

/* 进度条样式 */
.progress {
  width: 100%;
  height: 8rpx;
  background-color: var(--bg-gray);
  border-radius: var(--border-radius-base);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  transition: width var(--transition-base);
  border-radius: var(--border-radius-base);
}

/* 工作台网格 */
.workspace-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-base);
}

.workspace-item {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
  border: 1rpx solid transparent;
}

.workspace-item:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-base);
  border-color: var(--primary-light);
}

.workspace-icon {
  font-size: 64rpx;
  margin: 0 auto var(--spacing-sm);
  opacity: 0.8;
  display: block;
  text-align: center;
}

.workspace-title {
  font-size: var(--font-size-base);
  color: var(--text-color);
  font-weight: var(--font-weight-medium);
}

/* 列表项样式 */
.list-item {
  padding: var(--spacing-base) 0;
  border-bottom: 1rpx solid var(--border-light);
  display: flex;
  align-items: flex-start;
  transition: background-color var(--transition-fast);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background-color: var(--bg-gray);
  border-radius: var(--border-radius-base);
  margin: 0 calc(-1 * var(--spacing-base));
  padding: var(--spacing-base);
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: var(--font-size-base);
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.list-item-desc {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  line-height: 1.4;
}

.list-item-extra {
  margin-left: var(--spacing-sm);
  color: var(--text-muted);
  font-size: var(--font-size-xs);
  white-space: nowrap;
}

/* 加载和空状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
}

.loading-text {
  margin-left: var(--spacing-sm);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.empty {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-base);
  color: var(--text-muted);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: var(--spacing-base);
  opacity: 0.5;
}

.empty-text {
  font-size: var(--font-size-base);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .stats-grid {
    gap: var(--spacing-sm);
  }
  
  .stats-card {
    padding: var(--spacing-sm);
  }
  
  .stats-value {
    font-size: var(--font-size-lg);
  }
  
  .weather-temp {
    font-size: 60rpx;
  }
}
