# 自定义导航栏组件 (nav-bar)

一个功能完整的自定义导航栏组件，替代微信小程序默认的导航栏。

## 功能特性

- ✅ 自适应状态栏高度
- ✅ 支持返回和首页按钮
- ✅ 支持自定义左中右内容
- ✅ 多种主题样式
- ✅ 响应式设计
- ✅ 安全区域适配

## 基础用法

### 1. 在页面配置中引入组件

```json
{
  "navigationStyle": "custom",
  "usingComponents": {
    "nav-bar": "/components/common/nav-bar/nav-bar"
  }
}
```

### 2. 在页面模板中使用

```html
<!-- 基础用法 -->
<nav-bar title="页面标题" bind:navbarheight="onNavBarHeight"></nav-bar>

<!-- 页面内容需要设置顶部间距 -->
<view class="container" style="padding-top: {{navBarHeight}}px;">
  <!-- 页面内容 -->
</view>
```

### 3. 在页面 JS 中处理导航栏高度

```javascript
Page({
  data: {
    navBarHeight: 0
  },

  // 导航栏高度回调
  onNavBarHeight: function(e) {
    this.setData({
      navBarHeight: e.detail.navBarHeight
    })
  }
})
```

## 属性配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title | String | '' | 导航栏标题 |
| showBack | Boolean | true | 是否显示返回按钮 |
| showHome | Boolean | false | 是否显示首页按钮 |
| backIcon | String | '◀️' | 返回按钮图标 |
| homeIcon | String | '🏠' | 首页按钮图标 |
| backgroundColor | String | '#4CAF50' | 背景色 |
| textColor | String | '#ffffff' | 文字颜色 |
| fixed | Boolean | true | 是否固定在顶部 |

## 事件回调

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| navbarheight | 导航栏高度变化 | {navBarHeight, statusBarHeight, titleBarHeight} |
| backtap | 返回按钮点击 | - |
| hometap | 首页按钮点击 | - |

## 插槽支持

| 插槽名 | 说明 |
|--------|------|
| left | 左侧自定义内容 |
| center | 中间自定义内容 |
| right | 右侧自定义内容 |

## 使用示例

### 1. 基础导航栏

```html
<nav-bar title="页面标题"></nav-bar>
```

### 2. 首页导航栏（不显示返回按钮）

```html
<nav-bar title="首页" show-back="{{false}}"></nav-bar>
```

### 3. 带首页按钮的导航栏

```html
<nav-bar title="页面标题" show-home="{{true}}"></nav-bar>
```

### 4. 自定义颜色

```html
<nav-bar 
  title="页面标题" 
  background-color="#2196F3" 
  text-color="#ffffff">
</nav-bar>
```

### 5. 使用插槽自定义内容

```html
<nav-bar title="页面标题">
  <!-- 左侧插槽 -->
  <view slot="left">
    <text>自定义左侧</text>
  </view>
  
  <!-- 右侧插槽 -->
  <view slot="right">
    <text>自定义右侧</text>
  </view>
</nav-bar>
```

### 6. 处理按钮点击事件

```html
<nav-bar 
  title="页面标题" 
  bind:backtap="onBackTap"
  bind:hometap="onHomeTap">
</nav-bar>
```

```javascript
Page({
  // 自定义返回逻辑
  onBackTap: function() {
    // 自定义返回逻辑
    console.log('返回按钮被点击')
  },

  // 自定义首页逻辑
  onHomeTap: function() {
    // 自定义首页逻辑
    console.log('首页按钮被点击')
  }
})
```

## 主题样式

组件支持多种主题样式，可以通过 CSS 类名或属性来切换：

### 1. 默认主题（绿色）
```html
<nav-bar title="默认主题"></nav-bar>
```

### 2. 浅色主题
```html
<nav-bar title="浅色主题" theme="light"></nav-bar>
```

### 3. 透明主题
```html
<nav-bar title="透明主题" theme="transparent"></nav-bar>
```

### 4. 渐变主题
```html
<nav-bar title="渐变主题" theme="gradient"></nav-bar>
```

## 注意事项

1. **页面配置**：使用自定义导航栏时，必须在页面配置中设置 `"navigationStyle": "custom"`

2. **内容间距**：页面内容需要设置顶部间距，避免被导航栏遮挡

3. **状态栏适配**：组件会自动适配不同设备的状态栏高度

4. **安全区域**：组件已处理安全区域适配，支持刘海屏等特殊屏幕

5. **性能优化**：导航栏高度计算只在组件初始化时执行一次

## 样式定制

如需深度定制样式，可以通过以下方式：

1. **修改 CSS 变量**：在全局样式中覆盖相关变量
2. **添加自定义类名**：通过 `class` 属性添加自定义样式
3. **使用插槽**：通过插槽添加完全自定义的内容

## 兼容性

- 支持微信小程序基础库 2.0.0+
- 支持所有主流设备和屏幕尺寸
- 兼容 iOS 和 Android 平台
