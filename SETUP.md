# 项目启动指南

## 问题解决

### 1. 关于 npm 依赖

**问题**: 这个小程序框架不需要运行 `npm i`，因为它是纯原生小程序开发。

**解决方案**: 
- 项目已经配置了 `@vant/weapp` 作为可选依赖
- 如果不需要使用 Vant 组件，可以直接删除 `package.json` 和 `node_modules` 文件夹
- 当前使用 emoji 图标代替图片资源，无需额外依赖

### 2. 关于图标资源缺失

**问题**: tabBar 和页面中的图标文件不存在

**解决方案**: 
- ✅ 已使用自定义 tabBar 替代图片图标
- ✅ 已将所有页面中的图片图标替换为 emoji 表情
- ✅ 使用纯 CSS 和文字实现图标效果

## 快速启动

### 1. 导入项目
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目根目录
4. 输入 AppID（测试可使用测试号）

### 2. 配置项目
1. 修改 `project.config.json` 中的 `appid` 为你的小程序 AppID
2. 根据需要修改 `config/index.js` 中的配置

### 3. 编译运行
1. 在微信开发者工具中点击"编译"
2. 项目将自动编译并在模拟器中运行

## 当前功能状态

### ✅ 已完成功能
- [x] 完整的项目架构
- [x] 全局状态管理系统
- [x] 网络请求封装
- [x] 样式系统（CSS变量 + 通用样式类）
- [x] 自定义 tabBar（使用 emoji 图标）
- [x] 5个主要页面（首页、任务、工作台、消息、我的）
- [x] 工具函数库
- [x] 组件化开发示例
- [x] 响应式设计

### 📋 页面功能
- **首页**: 天气信息、农场统计、种植规模、快捷操作
- **任务页面**: 任务列表、搜索筛选、状态管理
- **工作台**: 功能模块导航、数据统计
- **消息中心**: 消息分类、搜索、已读管理
- **个人中心**: 用户信息、功能设置

### 🎨 UI 特性
- 使用 emoji 表情作为图标（无需图片资源）
- 完整的 CSS 变量系统
- 响应式布局适配
- 统一的视觉风格

## 图标说明

为了快速启动项目，我们使用了 emoji 表情替代图片图标：

### TabBar 图标
- 📋 任务
- ⚙️ 工作台  
- 🏠 总览
- 💬 消息
- 👤 我的

### 页面图标
- 📍 位置
- 🌾 农场
- 🏞️ 田块
- 📊 数据
- 🚜 农机
- 🌱 作物
- 📈 报表
- 等等...

## 自定义图标

如果需要使用自定义图标，可以：

### 方案一：替换为图片图标
1. 准备图标文件（建议 48x48px）
2. 放置在 `assets/icons/` 目录下
3. 修改对应页面的 `.wxml` 文件
4. 更新样式文件中的图标样式

### 方案二：使用字体图标
1. 引入字体图标文件（如 iconfont）
2. 在 `app.wxss` 中引入字体文件
3. 替换页面中的 emoji 为字体图标类名

### 方案三：使用 Vant 图标
1. 项目已安装 `@vant/weapp`
2. 在需要的页面配置中引入 Vant 组件
3. 使用 `<van-icon>` 组件

## 开发建议

### 1. 开发流程
1. 先确保基础功能正常运行
2. 根据业务需求添加新页面和功能
3. 逐步替换 mock 数据为真实 API
4. 优化用户体验和性能

### 2. 代码规范
- 遵循现有的文件结构和命名规范
- 使用提供的工具函数和样式类
- 保持代码注释的完整性

### 3. 测试建议
- 在不同设备上测试响应式效果
- 测试网络异常情况的处理
- 验证用户交互的流畅性

## 常见问题

### Q: 为什么不使用图片图标？
A: 为了快速启动项目，避免图片资源缺失的问题。emoji 图标在所有设备上都能正常显示，且无需额外资源。

### Q: 如何添加新页面？
A: 参考现有页面结构，创建对应的 `.wxml`、`.js`、`.wxss`、`.json` 文件，并在 `app.json` 中注册。

### Q: 如何修改主题色彩？
A: 修改 `styles/variables.wxss` 中的 CSS 变量，或在 `config/index.js` 中配置。

### Q: 数据从哪里来？
A: 当前使用 mock 数据，在 `api/index.js` 中定义。实际开发时替换为真实 API 接口。

## 技术支持

如有问题，请查看：
1. [README.md](./README.md) - 项目介绍
2. [USAGE.md](./USAGE.md) - 详细使用指南
3. 代码注释和示例

---

**现在就可以开始开发了！** 🚀
