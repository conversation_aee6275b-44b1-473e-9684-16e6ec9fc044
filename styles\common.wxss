/* 通用组件样式 */

/* 卡片样式 */
.card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-base);
  margin-bottom: var(--spacing-base);
}

.card-header {
  padding-bottom: var(--spacing-sm);
  border-bottom: 1rpx solid var(--border-light);
  margin-bottom: var(--spacing-sm);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
}

.card-body {
  padding: var(--spacing-sm) 0;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-base);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-align: center;
  transition: all var(--transition-fast);
  border: none;
  cursor: pointer;
  min-height: var(--btn-height-base);
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-primary:active {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--text-white);
}

.btn-success {
  background-color: var(--success-color);
  color: var(--text-white);
}

.btn-warning {
  background-color: var(--warning-color);
  color: var(--text-white);
}

.btn-danger {
  background-color: var(--danger-color);
  color: var(--text-white);
}

.btn-outline {
  background-color: transparent;
  border: 1rpx solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:active {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
  min-height: var(--btn-height-sm);
}

.btn-lg {
  padding: var(--spacing-base) var(--spacing-lg);
  font-size: var(--font-size-lg);
  min-height: var(--btn-height-lg);
}

.btn-block {
  width: 100%;
}

.btn-round {
  border-radius: var(--border-radius-round);
}

/* 输入框样式 */
.input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-base);
  border: 1rpx solid var(--border-color);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-base);
  background-color: var(--bg-white);
  min-height: var(--input-height-base);
}

.input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.input-group {
  margin-bottom: var(--spacing-base);
}

.input-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* 列表样式 */
.list {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.list-item {
  padding: var(--spacing-base);
  border-bottom: 1rpx solid var(--border-light);
  display: flex;
  align-items: center;
  transition: background-color var(--transition-fast);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background-color: var(--bg-gray);
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: var(--font-size-base);
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.list-item-desc {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.list-item-extra {
  margin-left: var(--spacing-sm);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

/* 标签样式 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.tag-primary {
  background-color: var(--primary-light);
  color: var(--primary-dark);
}

.tag-success {
  background-color: #E8F5E8;
  color: var(--success-color);
}

.tag-warning {
  background-color: #FFF3E0;
  color: var(--warning-color);
}

.tag-danger {
  background-color: #FFEBEE;
  color: var(--danger-color);
}

/* 进度条样式 */
.progress {
  width: 100%;
  height: 8rpx;
  background-color: var(--bg-gray);
  border-radius: var(--border-radius-base);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  transition: width var(--transition-base);
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: var(--border-light);
  margin: var(--spacing-base) 0;
}

/* 空状态 */
.empty {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-base);
  color: var(--text-muted);
}

.empty-icon {
  font-size: var(--font-size-xxl);
  margin-bottom: var(--spacing-base);
}

.empty-text {
  font-size: var(--font-size-base);
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
}

.loading-text {
  margin-left: var(--spacing-sm);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}
