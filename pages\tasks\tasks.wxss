/* 任务页面样式 */

.container {
  background-color: var(--bg-color);
  min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
  padding: var(--spacing-base);
  background-color: var(--bg-white);
  border-bottom: 1rpx solid var(--border-light);
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: var(--bg-gray);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-sm) var(--spacing-base);
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: var(--spacing-sm);
  opacity: 0.5;
}

.search-input {
  flex: 1;
  font-size: var(--font-size-base);
  color: var(--text-color);
  background: transparent;
  border: none;
  outline: none;
}

.search-input::placeholder {
  color: var(--text-muted);
}

.search-clear {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: var(--spacing-sm);
}

.search-clear image {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 任务列表 */
.task-list {
  padding: var(--spacing-base);
}

.task-item {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-base);
  margin-bottom: var(--spacing-base);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.task-item:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-base);
}

/* 任务头部 */
.task-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.task-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  flex: 1;
  margin-right: var(--spacing-sm);
}

.task-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
}

.task-status-pending {
  background-color: #FFF3E0;
  color: var(--warning-color);
}

.task-status-progress {
  background-color: #E3F2FD;
  color: var(--info-color);
}

.task-status-completed {
  background-color: #E8F5E8;
  color: var(--success-color);
}

/* 任务详情 */
.task-details {
  margin-bottom: var(--spacing-base);
}

.task-date,
.task-location {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.task-date:last-child,
.task-location:last-child {
  margin-bottom: 0;
}

.task-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: var(--spacing-xs);
  opacity: 0.6;
}

/* 进度部分 */
.task-progress-section {
  margin-bottom: var(--spacing-base);
}

.task-progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.task-progress-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.task-progress-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
}

.progress {
  width: 100%;
  height: 8rpx;
  background-color: var(--bg-gray);
  border-radius: var(--border-radius-base);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  transition: width var(--transition-base);
  border-radius: var(--border-radius-base);
}

/* 任务操作 */
.task-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.task-actions .btn {
  flex: 1;
  text-align: center;
  min-height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.patrol-btn {
  background-color: var(--info-color);
  color: var(--text-white);
  border-radius: var(--border-radius-round);
  padding: var(--spacing-sm) var(--spacing-base);
  font-size: var(--font-size-sm);
  flex: none;
  min-width: 120rpx;
}

.patrol-btn:active {
  background-color: var(--secondary-dark);
}

/* 浮动操作按钮 */
.fab {
  position: fixed;
  right: 32rpx;
  bottom: 120rpx;
  width: 112rpx;
  height: 112rpx;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
  z-index: 100;
  transition: all var(--transition-fast);
}

.fab:active {
  transform: scale(0.95);
  box-shadow: var(--shadow-xl);
}

.fab image {
  width: 48rpx;
  height: 48rpx;
  filter: brightness(0) invert(1);
}

/* 空状态 */
.empty {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-base);
  color: var(--text-muted);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: var(--spacing-base);
  opacity: 0.5;
}

.empty-text {
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-lg);
}

.empty .btn {
  margin: 0 auto;
  max-width: 200rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
}

.loading-text {
  margin-left: var(--spacing-sm);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .task-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .task-actions .btn {
    width: 100%;
  }
  
  .fab {
    right: 24rpx;
    bottom: 100rpx;
    width: 96rpx;
    height: 96rpx;
  }
  
  .fab image {
    width: 40rpx;
    height: 40rpx;
  }
}
