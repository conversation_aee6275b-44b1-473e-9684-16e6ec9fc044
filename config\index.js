// 全局配置文件
const config = {
  // 环境配置
  env: 'development', // development, production
  
  // API配置
  api: {
    baseUrl: 'https://api.example.com',
    timeout: 10000,
    retryCount: 3,
    
    // 接口地址
    endpoints: {
      // 用户相关
      login: '/auth/login',
      logout: '/auth/logout',
      userInfo: '/user/info',
      
      // 农场相关
      farmInfo: '/farm/info',
      farmStats: '/farm/stats',
      
      // 任务相关
      taskList: '/tasks',
      taskDetail: '/tasks/:id',
      taskUpdate: '/tasks/:id',
      
      // 作物相关
      cropList: '/crops',
      cropStats: '/crops/stats',
      
      // 设备相关
      deviceList: '/devices',
      deviceStatus: '/devices/:id/status',
      
      // 天气相关
      weather: '/weather',
      
      // 消息相关
      messageList: '/messages',
      messageRead: '/messages/:id/read'
    }
  },
  
  // 存储配置
  storage: {
    prefix: 'wxpro_',
    keys: {
      userInfo: 'user_info',
      token: 'access_token',
      farmInfo: 'farm_info',
      settings: 'app_settings'
    }
  },
  
  // 页面配置
  pages: {
    // 默认页面
    defaultPage: '/pages/index/index',
    
    // 登录页面
    loginPage: '/pages/login/login',
    
    // 错误页面
    errorPage: '/pages/error/error'
  },
  
  // 主题配置
  theme: {
    primaryColor: '#4CAF50',
    secondaryColor: '#2196F3',
    
    // 字体配置
    fontFamily: '-apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimSun, sans-serif',
    
    // 动画配置
    animation: {
      duration: 300,
      easing: 'ease-out'
    }
  },
  
  // 功能开关
  features: {
    // 是否启用调试模式
    debug: true,
    
    // 是否启用错误上报
    errorReport: true,
    
    // 是否启用性能监控
    performance: true,
    
    // 是否启用用户行为统计
    analytics: true,
    
    // 是否启用推送通知
    notification: true
  },
  
  // 业务配置
  business: {
    // 农场类型
    farmTypes: [
      { id: 1, name: '粮食作物', icon: 'grain' },
      { id: 2, name: '经济作物', icon: 'economic' },
      { id: 3, name: '蔬菜作物', icon: 'vegetable' },
      { id: 4, name: '果树作物', icon: 'fruit' }
    ],
    
    // 作物类型
    cropTypes: [
      { id: 1, name: '小麦', category: 'grain', season: 'spring' },
      { id: 2, name: '玉米', category: 'grain', season: 'summer' },
      { id: 3, name: '大豆', category: 'economic', season: 'summer' },
      { id: 4, name: '水稻', category: 'grain', season: 'summer' }
    ],
    
    // 任务类型
    taskTypes: [
      { id: 1, name: '播种', icon: 'seed', color: '#4CAF50' },
      { id: 2, name: '浇水', icon: 'water', color: '#2196F3' },
      { id: 3, name: '施肥', icon: 'fertilizer', color: '#FF9800' },
      { id: 4, name: '除草', icon: 'weed', color: '#795548' },
      { id: 5, name: '收获', icon: 'harvest', color: '#FF5722' }
    ],
    
    // 设备类型
    deviceTypes: [
      { id: 1, name: '拖拉机', icon: 'tractor' },
      { id: 2, name: '播种机', icon: 'seeder' },
      { id: 3, name: '收割机', icon: 'harvester' },
      { id: 4, name: '灌溉设备', icon: 'irrigation' }
    ]
  },
  
  // 地图配置
  map: {
    // 默认中心点（酒泉市金塔县）
    center: {
      latitude: 39.9842,
      longitude: 98.9026
    },
    
    // 默认缩放级别
    zoom: 12,
    
    // 地图类型
    mapType: 'satellite'
  },
  
  // 分享配置
  share: {
    title: '智慧农场管理系统',
    desc: '专业的农场管理解决方案',
    imageUrl: '/assets/images/share-logo.png'
  },
  
  // 版本信息
  version: {
    app: '1.0.0',
    api: '1.0.0',
    build: '20250826'
  }
}

// 根据环境切换配置
if (config.env === 'production') {
  config.api.baseUrl = 'https://prod-api.example.com'
  config.features.debug = false
}

export default config
