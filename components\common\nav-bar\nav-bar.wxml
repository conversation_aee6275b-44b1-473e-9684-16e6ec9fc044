<!-- 自定义导航栏组件 -->
<view class="nav-bar" style="height: {{navBarHeight}}px; padding-top: {{statusBarHeight}}px;">
  <!-- 状态栏占位 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  
  <!-- 导航栏内容 -->
  <view class="nav-bar-content" style="height: {{titleBarHeight}}px;">
    <!-- 左侧内容 -->
    <view class="nav-bar-left">
      <view wx:if="{{showBack}}" class="nav-back-btn" bindtap="onBackTap">
        <text class="nav-back-icon">{{backIcon}}</text>
      </view>
      <view wx:if="{{showHome}}" class="nav-home-btn" bindtap="onHomeTap">
        <text class="nav-home-icon">{{homeIcon}}</text>
      </view>
      <slot name="left"></slot>
    </view>
    
    <!-- 中间标题 -->
    <view class="nav-bar-center">
      <text class="nav-bar-title">{{title}}</text>
      <slot name="center"></slot>
    </view>
    
    <!-- 右侧内容 -->
    <view class="nav-bar-right">
      <slot name="right"></slot>
    </view>
  </view>
</view>
