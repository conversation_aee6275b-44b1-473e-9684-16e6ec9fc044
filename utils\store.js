// 全局状态管理
class Store {
  constructor() {
    this.state = {}
    this.listeners = {}
    this.middlewares = []
  }

  // 初始化store
  init() {
    this.state = {
      // 用户信息
      user: {
        info: null,
        isLogin: false,
        token: null
      },
      
      // 农场信息
      farm: {
        current: null,
        list: [],
        stats: null
      },
      
      // 任务信息
      tasks: {
        list: [],
        current: null,
        stats: {
          total: 0,
          completed: 0,
          pending: 0,
          inProgress: 0
        }
      },
      
      // 作物信息
      crops: {
        list: [],
        stats: null
      },
      
      // 设备信息
      devices: {
        list: [],
        stats: null
      },
      
      // 天气信息
      weather: {
        current: null,
        forecast: []
      },
      
      // 消息信息
      messages: {
        list: [],
        unreadCount: 0
      },
      
      // 应用状态
      app: {
        loading: false,
        error: null,
        networkStatus: 'online'
      }
    }
    
    // 从本地存储恢复状态
    this.restoreFromStorage()
  }

  // 获取状态
  getState(path) {
    if (!path) return this.state
    
    const keys = path.split('.')
    let result = this.state
    
    for (const key of keys) {
      if (result && typeof result === 'object' && key in result) {
        result = result[key]
      } else {
        return undefined
      }
    }
    
    return result
  }

  // 设置状态
  setState(updates, options = {}) {
    const { silent = false, persist = true } = options
    
    // 应用中间件
    let finalUpdates = updates
    for (const middleware of this.middlewares) {
      finalUpdates = middleware(finalUpdates, this.state)
    }
    
    // 深度合并状态
    this.state = this.deepMerge(this.state, finalUpdates)
    
    // 持久化到本地存储
    if (persist) {
      this.saveToStorage()
    }
    
    // 通知监听器
    if (!silent) {
      this.notifyListeners(updates)
    }
    
    return this.state
  }

  // 订阅状态变化
  subscribe(path, callback) {
    if (typeof path === 'function') {
      callback = path
      path = '*'
    }
    
    if (!this.listeners[path]) {
      this.listeners[path] = []
    }
    
    this.listeners[path].push(callback)
    
    // 返回取消订阅函数
    return () => {
      const index = this.listeners[path].indexOf(callback)
      if (index > -1) {
        this.listeners[path].splice(index, 1)
      }
    }
  }

  // 取消订阅
  unsubscribe(path, callback) {
    if (this.listeners[path]) {
      const index = this.listeners[path].indexOf(callback)
      if (index > -1) {
        this.listeners[path].splice(index, 1)
      }
    }
  }

  // 通知监听器
  notifyListeners(updates) {
    // 通知全局监听器
    if (this.listeners['*']) {
      this.listeners['*'].forEach(callback => {
        try {
          callback(this.state, updates)
        } catch (error) {
          console.error('Store listener error:', error)
        }
      })
    }
    
    // 通知特定路径监听器
    Object.keys(updates).forEach(key => {
      if (this.listeners[key]) {
        this.listeners[key].forEach(callback => {
          try {
            callback(this.getState(key), updates[key])
          } catch (error) {
            console.error('Store listener error:', error)
          }
        })
      }
    })
  }

  // 添加中间件
  use(middleware) {
    this.middlewares.push(middleware)
  }

  // 深度合并对象
  deepMerge(target, source) {
    const result = { ...target }
    
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = this.deepMerge(target[key] || {}, source[key])
        } else {
          result[key] = source[key]
        }
      }
    }
    
    return result
  }

  // 保存到本地存储
  saveToStorage() {
    try {
      const storageData = {
        user: this.state.user,
        farm: this.state.farm,
        settings: this.state.settings
      }
      
      wx.setStorageSync('wxpro_store', JSON.stringify(storageData))
    } catch (error) {
      console.error('Save to storage failed:', error)
    }
  }

  // 从本地存储恢复
  restoreFromStorage() {
    try {
      const storageData = wx.getStorageSync('wxpro_store')
      if (storageData) {
        const data = JSON.parse(storageData)
        this.setState(data, { silent: true, persist: false })
      }
    } catch (error) {
      console.error('Restore from storage failed:', error)
    }
  }

  // 清空状态
  clear() {
    this.state = {}
    this.listeners = {}
    wx.removeStorageSync('wxpro_store')
  }

  // 重置状态
  reset() {
    this.init()
  }
}

// 创建全局store实例
const store = new Store()

// 添加日志中间件
if (wx.getSystemInfoSync().platform === 'devtools') {
  store.use((updates, state) => {
    console.group('Store Update')
    console.log('Updates:', updates)
    console.log('New State:', state)
    console.groupEnd()
    return updates
  })
}

export default store
