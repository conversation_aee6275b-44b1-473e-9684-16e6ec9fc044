// 自定义导航栏组件
Component({
  // 组件属性
  properties: {
    // 标题文字
    title: {
      type: String,
      value: ''
    },
    
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      value: true
    },
    
    // 是否显示首页按钮
    showHome: {
      type: Boolean,
      value: false
    },
    
    // 返回按钮图标
    backIcon: {
      type: String,
      value: '◀️'
    },
    
    // 首页按钮图标
    homeIcon: {
      type: String,
      value: '🏠'
    },
    
    // 背景色
    backgroundColor: {
      type: String,
      value: '#4CAF50'
    },
    
    // 文字颜色
    textColor: {
      type: String,
      value: '#ffffff'
    },
    
    // 是否固定在顶部
    fixed: {
      type: Boolean,
      value: true
    }
  },

  // 组件数据
  data: {
    statusBarHeight: 0,
    titleBarHeight: 44,
    navBarHeight: 0
  },

  // 组件生命周期
  lifetimes: {
    attached: function() {
      this.initNavBar()
    }
  },

  // 组件方法
  methods: {
    // 初始化导航栏
    initNavBar: function() {
      const systemInfo = wx.getSystemInfoSync()
      const statusBarHeight = systemInfo.statusBarHeight || 20
      const titleBarHeight = 44
      const navBarHeight = statusBarHeight + titleBarHeight
      
      this.setData({
        statusBarHeight,
        titleBarHeight,
        navBarHeight
      })
      
      // 通知父组件导航栏高度
      this.triggerEvent('navbarheight', {
        navBarHeight,
        statusBarHeight,
        titleBarHeight
      })
    },
    
    // 返回按钮点击
    onBackTap: function() {
      this.triggerEvent('backtap')
      
      // 默认行为：返回上一页
      const pages = getCurrentPages()
      if (pages.length > 1) {
        wx.navigateBack()
      } else {
        // 如果是第一个页面，跳转到首页
        wx.switchTab({
          url: '/pages/index/index'
        })
      }
    },
    
    // 首页按钮点击
    onHomeTap: function() {
      this.triggerEvent('hometap')
      
      // 默认行为：跳转到首页
      wx.switchTab({
        url: '/pages/index/index'
      })
    },
    
    // 设置标题
    setTitle: function(title) {
      this.setData({
        title: title
      })
    },
    
    // 设置背景色
    setBackgroundColor: function(backgroundColor) {
      this.setData({
        backgroundColor: backgroundColor
      })
    },
    
    // 设置文字颜色
    setTextColor: function(textColor) {
      this.setData({
        textColor: textColor
      })
    }
  }
})
