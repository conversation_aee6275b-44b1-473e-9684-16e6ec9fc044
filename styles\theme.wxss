/* 主题样式 */

/* 农场主题色彩 */
.theme-farm {
  --primary-color: #4CAF50;
  --primary-light: #81C784;
  --primary-dark: #388E3C;
  --accent-color: #8BC34A;
}

/* 天气卡片样式 */
.weather-card {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: var(--text-white);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-base);
  position: relative;
  overflow: hidden;
}

.weather-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.weather-location {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.weather-location-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: var(--spacing-xs);
}

.weather-location-text {
  font-size: var(--font-size-base);
  opacity: 0.9;
}

.weather-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.weather-temp {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
}

.weather-condition {
  font-size: var(--font-size-base);
  opacity: 0.8;
}

.weather-farm {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

.weather-farm-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: var(--spacing-xs);
}

/* 统计卡片样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-base);
  margin-bottom: var(--spacing-base);
}

.stats-card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-base);
  text-align: center;
  box-shadow: var(--shadow-sm);
}

.stats-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
}

.stats-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
}

.stats-unit {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-left: var(--spacing-xs);
}

/* 数据展示卡片 */
.data-card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-base);
  margin-bottom: var(--spacing-base);
  box-shadow: var(--shadow-sm);
}

.data-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) 0;
}

.data-row:not(:last-child) {
  border-bottom: 1rpx solid var(--border-light);
}

.data-label {
  display: flex;
  align-items: center;
  font-size: var(--font-size-base);
  color: var(--text-color);
}

.data-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: var(--spacing-sm);
}

.data-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
}

/* 种植数据样式 */
.crop-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) 0;
}

.crop-name {
  font-size: var(--font-size-base);
  color: var(--text-color);
}

.crop-progress {
  flex: 1;
  margin: 0 var(--spacing-base);
}

.crop-area {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  min-width: 80rpx;
  text-align: right;
}

/* 任务状态样式 */
.task-status {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.task-status-pending {
  background-color: #FFF3E0;
  color: var(--warning-color);
}

.task-status-progress {
  background-color: #E3F2FD;
  color: var(--info-color);
}

.task-status-completed {
  background-color: #E8F5E8;
  color: var(--success-color);
}

/* 工作台网格 */
.workspace-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-base);
  padding: var(--spacing-base);
}

.workspace-item {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.workspace-item:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-base);
}

.workspace-icon {
  width: 64rpx;
  height: 64rpx;
  margin: 0 auto var(--spacing-sm);
}

.workspace-title {
  font-size: var(--font-size-base);
  color: var(--text-color);
  font-weight: var(--font-weight-medium);
}

/* 消息样式 */
.message-item {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-base);
  margin-bottom: var(--spacing-sm);
  box-shadow: var(--shadow-sm);
}

.message-title {
  font-size: var(--font-size-base);
  color: var(--text-color);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
}

.message-content {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: var(--spacing-xs);
}

.message-time {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  text-align: right;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: var(--text-white);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-base);
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: var(--spacing-base);
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
}

.user-id {
  font-size: var(--font-size-sm);
  opacity: 0.8;
  margin-bottom: var(--spacing-xs);
}

.user-wechat {
  font-size: var(--font-size-sm);
  opacity: 0.8;
}
