<!-- 任务页面 -->
<nav-bar title="任务管理" bind:navbarheight="onNavBarHeight"></nav-bar>

<view class="container" style="padding-top: {{navBarHeight + 'px'}}">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <text class="search-icon">🔍</text>
      <input class="search-input"
             placeholder="请输入任务名称"
             value="{{searchKeyword}}"
             bindinput="onSearchInput"
             bindconfirm="onSearchConfirm" />
      <view wx:if="{{searchKeyword}}" class="search-clear" bindtap="onSearchClear">
        <text>❌</text>
      </view>
    </view>
  </view>

  <!-- 任务列表 -->
  <view class="task-list">
    <view wx:for="{{taskList}}" wx:key="id" class="task-item" bindtap="onTaskTap" data-task="{{item}}">
      <!-- 任务基本信息 -->
      <view class="task-header">
        <view class="task-title">{{item.name}}</view>
        <view class="task-status task-status-{{item.status}}">
          <text wx:if="{{item.status === 'pending'}}">待开始</text>
          <text wx:if="{{item.status === 'progress'}}">进行中</text>
          <text wx:if="{{item.status === 'completed'}}">已完成</text>
        </view>
      </view>

      <!-- 任务详情 -->
      <view class="task-details">
        <view class="task-date">
          <text class="task-icon">📅</text>
          <text>{{item.date}}</text>
        </view>

        <view class="task-location">
          <text class="task-icon">📍</text>
          <text>{{item.location}}</text>
        </view>
      </view>

      <!-- 进度条 -->
      <view class="task-progress-section">
        <view class="task-progress-header">
          <text class="task-progress-label">进度</text>
          <text class="task-progress-value">{{item.progress}}%</text>
        </view>
        <view class="progress">
          <view class="progress-bar" style="width: {{item.progress}}%; background-color: {{getProgressColor(item.progress, item.status)}};"></view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="task-actions">
        <view wx:if="{{item.status === 'pending'}}" class="btn btn-primary btn-sm" bindtap="onStartTask" data-id="{{item.id}}">
          开始任务
        </view>
        <view wx:if="{{item.status === 'progress'}}" class="btn btn-success btn-sm" bindtap="onCompleteTask" data-id="{{item.id}}">
          完成任务
        </view>
        <view wx:if="{{item.status === 'completed'}}" class="btn btn-outline btn-sm" bindtap="onViewTask" data-id="{{item.id}}">
          查看详情
        </view>
        
        <!-- 巡田打卡按钮 -->
        <view wx:if="{{item.type === 'patrol'}}" class="btn btn-round btn-primary patrol-btn" bindtap="onPatrolCheckIn" data-id="{{item.id}}">
          巡田打卡
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{!loading && taskList.length === 0}}" class="empty">
    <view class="empty-icon">📋</view>
    <view class="empty-text">{{searchKeyword ? '未找到相关任务' : '暂无任务'}}</view>
    <view wx:if="{{!searchKeyword}}" class="btn btn-primary" bindtap="onCreateTask">
      创建任务
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 浮动操作按钮 -->
<view class="fab" bindtap="onCreateTask">
  <text>➕</text>
</view>
