// 网络请求封装
import config from '../config/index.js'
import store from './store.js'

class Request {
  constructor() {
    this.baseUrl = config.api.baseUrl
    this.timeout = config.api.timeout
    this.retryCount = config.api.retryCount
    this.interceptors = {
      request: [],
      response: []
    }
    
    // 添加默认拦截器
    this.addDefaultInterceptors()
  }

  // 添加默认拦截器
  addDefaultInterceptors() {
    // 请求拦截器 - 添加token
    this.interceptors.request.push((config) => {
      const token = store.getState('user.token')
      if (token) {
        config.header = config.header || {}
        config.header.Authorization = `Bearer ${token}`
      }
      return config
    })

    // 请求拦截器 - 显示loading
    this.interceptors.request.push((config) => {
      if (config.loading !== false) {
        wx.showLoading({
          title: config.loadingText || '加载中...',
          mask: true
        })
      }
      return config
    })

    // 响应拦截器 - 隐藏loading
    this.interceptors.response.push((response) => {
      wx.hideLoading()
      return response
    })

    // 响应拦截器 - 统一错误处理
    this.interceptors.response.push((response) => {
      const { statusCode, data } = response
      
      // HTTP状态码检查
      if (statusCode >= 200 && statusCode < 300) {
        // 业务状态码检查
        if (data.code === 0 || data.success) {
          return data
        } else {
          return Promise.reject(new Error(data.message || '请求失败'))
        }
      } else if (statusCode === 401) {
        // 未授权，清除用户信息并跳转登录
        store.setState({
          user: {
            info: null,
            isLogin: false,
            token: null
          }
        })
        wx.navigateTo({
          url: config.pages.loginPage
        })
        return Promise.reject(new Error('登录已过期，请重新登录'))
      } else {
        return Promise.reject(new Error(`网络错误 ${statusCode}`))
      }
    })
  }

  // 添加请求拦截器
  addRequestInterceptor(interceptor) {
    this.interceptors.request.push(interceptor)
  }

  // 添加响应拦截器
  addResponseInterceptor(interceptor) {
    this.interceptors.response.push(interceptor)
  }

  // 执行请求拦截器
  executeRequestInterceptors(config) {
    let result = config
    for (const interceptor of this.interceptors.request) {
      result = interceptor(result)
    }
    return result
  }

  // 执行响应拦截器
  executeResponseInterceptors(response) {
    let result = response
    for (const interceptor of this.interceptors.response) {
      try {
        result = interceptor(result)
      } catch (error) {
        return Promise.reject(error)
      }
    }
    return result
  }

  // 基础请求方法
  request(options) {
    return new Promise((resolve, reject) => {
      // 合并配置
      const config = {
        url: this.baseUrl + options.url,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          ...options.header
        },
        timeout: options.timeout || this.timeout,
        ...options
      }

      // 执行请求拦截器
      const finalConfig = this.executeRequestInterceptors(config)

      // 发起请求
      this.doRequest(finalConfig, 0)
        .then(response => {
          // 执行响应拦截器
          return this.executeResponseInterceptors(response)
        })
        .then(resolve)
        .catch(reject)
    })
  }

  // 执行请求（支持重试）
  doRequest(config, retryCount) {
    return new Promise((resolve, reject) => {
      wx.request({
        ...config,
        success: (response) => {
          resolve(response)
        },
        fail: (error) => {
          console.error('Request failed:', error)
          
          // 网络错误重试
          if (retryCount < this.retryCount && this.shouldRetry(error)) {
            console.log(`Retrying request, attempt ${retryCount + 1}`)
            setTimeout(() => {
              this.doRequest(config, retryCount + 1)
                .then(resolve)
                .catch(reject)
            }, 1000 * (retryCount + 1))
          } else {
            wx.hideLoading()
            reject(new Error(error.errMsg || '网络请求失败'))
          }
        }
      })
    })
  }

  // 判断是否应该重试
  shouldRetry(error) {
    const retryableErrors = [
      'request:fail timeout',
      'request:fail',
      'request:fail net::ERR_NETWORK_CHANGED'
    ]
    
    return retryableErrors.some(err => error.errMsg && error.errMsg.includes(err))
  }

  // GET请求
  get(url, params = {}, options = {}) {
    const queryString = this.buildQueryString(params)
    const fullUrl = queryString ? `${url}?${queryString}` : url
    
    return this.request({
      url: fullUrl,
      method: 'GET',
      ...options
    })
  }

  // POST请求
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    })
  }

  // PUT请求
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  }

  // DELETE请求
  delete(url, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      ...options
    })
  }

  // 上传文件
  upload(url, filePath, options = {}) {
    return new Promise((resolve, reject) => {
      const config = {
        url: this.baseUrl + url,
        filePath,
        name: options.name || 'file',
        formData: options.formData || {},
        header: options.header || {},
        ...options
      }

      // 添加token
      const token = store.getState('user.token')
      if (token) {
        config.header.Authorization = `Bearer ${token}`
      }

      // 显示上传进度
      if (options.showProgress !== false) {
        wx.showLoading({
          title: '上传中...',
          mask: true
        })
      }

      wx.uploadFile({
        ...config,
        success: (response) => {
          wx.hideLoading()
          try {
            const data = JSON.parse(response.data)
            if (data.code === 0 || data.success) {
              resolve(data)
            } else {
              reject(new Error(data.message || '上传失败'))
            }
          } catch (error) {
            reject(new Error('响应数据格式错误'))
          }
        },
        fail: (error) => {
          wx.hideLoading()
          reject(new Error(error.errMsg || '上传失败'))
        }
      })
    })
  }

  // 下载文件
  download(url, options = {}) {
    return new Promise((resolve, reject) => {
      wx.downloadFile({
        url: this.baseUrl + url,
        ...options,
        success: resolve,
        fail: reject
      })
    })
  }

  // 构建查询字符串
  buildQueryString(params) {
    const pairs = []
    for (const key in params) {
      if (params.hasOwnProperty(key) && params[key] !== undefined && params[key] !== null) {
        pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      }
    }
    return pairs.join('&')
  }
}

// 创建请求实例
const request = new Request()

export default request
