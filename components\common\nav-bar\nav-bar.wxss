/* 自定义导航栏样式 */

.nav-bar {
  width: 100%;
  background-color: var(--primary-color);
  color: #ffffff;
  z-index: 9999;
}

.nav-bar[fixed="true"] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

.status-bar {
  width: 100%;
}

.nav-bar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-base);
  position: relative;
}

/* 左侧区域 */
.nav-bar-left {
  display: flex;
  align-items: center;
  min-width: 120rpx;
  justify-content: flex-start;
}

.nav-back-btn,
.nav-home-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  margin-right: var(--spacing-xs);
  border-radius: 50%;
  transition: background-color var(--transition-fast);
}

.nav-back-btn:active,
.nav-home-btn:active {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-back-icon,
.nav-home-icon {
  font-size: 32rpx;
  color: #ffffff;
}

/* 中间标题区域 */
.nav-bar-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-base);
}

.nav-bar-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: #ffffff;
  text-align: center;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 右侧区域 */
.nav-bar-right {
  display: flex;
  align-items: center;
  min-width: 120rpx;
  justify-content: flex-end;
}

/* 主题变体 */
.nav-bar[theme="light"] {
  background-color: #ffffff;
  color: var(--text-color);
  border-bottom: 1rpx solid var(--border-light);
}

.nav-bar[theme="light"] .nav-bar-title {
  color: var(--text-color);
}

.nav-bar[theme="light"] .nav-back-icon,
.nav-bar[theme="light"] .nav-home-icon {
  color: var(--text-color);
}

.nav-bar[theme="light"] .nav-back-btn:active,
.nav-bar[theme="light"] .nav-home-btn:active {
  background-color: var(--bg-gray);
}

/* 透明主题 */
.nav-bar[theme="transparent"] {
  background-color: transparent;
  color: #ffffff;
}

.nav-bar[theme="transparent"] .nav-bar-title {
  color: #ffffff;
}

/* 渐变主题 */
.nav-bar[theme="gradient"] {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .nav-bar-content {
    padding: 0 var(--spacing-sm);
  }
  
  .nav-bar-title {
    font-size: var(--font-size-base);
    max-width: 300rpx;
  }
  
  .nav-back-btn,
  .nav-home-btn {
    width: 56rpx;
    height: 56rpx;
  }
  
  .nav-back-icon,
  .nav-home-icon {
    font-size: 28rpx;
  }
}

/* 安全区域适配 */
.nav-bar {
  padding-bottom: env(safe-area-inset-bottom);
}
