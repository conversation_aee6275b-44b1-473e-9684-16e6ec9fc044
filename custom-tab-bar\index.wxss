/* 自定义 tabBar 样式 */

.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 98rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 8rpx 0;
  transition: all 0.2s ease;
}

.tab-bar-item.selected {
  color: #4CAF50;
}

.tab-bar-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4rpx;
}

.iconfont {
  font-size: 44rpx;
  color: #999999;
  transition: color 0.2s ease;
}

.tab-bar-item.selected .iconfont {
  color: #4CAF50;
}

.tab-bar-text {
  font-size: 20rpx;
  color: #999999;
  transition: color 0.2s ease;
}

.tab-bar-item.selected .tab-bar-text {
  color: #4CAF50;
}

.tab-bar-badge {
  position: absolute;
  top: 4rpx;
  right: 20rpx;
  background-color: #ff4444;
  color: #ffffff;
  font-size: 18rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  transform: scale(0.8);
}

/* 使用简单的字符图标代替 iconfont */
.icon-task::before { content: "📋"; }
.icon-task-fill::before { content: "📋"; }
.icon-apps::before { content: "⚙️"; }
.icon-apps-fill::before { content: "⚙️"; }
.icon-home::before { content: "🏠"; }
.icon-home-fill::before { content: "🏠"; }
.icon-chat::before { content: "💬"; }
.icon-chat-fill::before { content: "💬"; }
.icon-user::before { content: "👤"; }
.icon-user-fill::before { content: "👤"; }

.iconfont::before {
  font-size: 40rpx;
}
