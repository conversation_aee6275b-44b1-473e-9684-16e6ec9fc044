---
description: "CMAF 框架的编码风格、命名规范与性能优化最佳实践。用于指导 Cursor 编写高质量、一致性且高性能的代码。"
globs:
  # 应用于所有小程序源代码文件
  - './**/*.{js,ts}'
  - './**/*.wxml'
  - './**/*.wxss'
  - './**/*.json'
alwaysApply: true
---

# CMAF 编码规范与最佳实践

## 1. JavaScript / TypeScript 编码规范

- **命名**:
    - 变量和函数名使用 `camelCase`。
    - 常量使用 `UPPER_CASE_SNAKE_CASE`。
    - 文件名使用 `kebab-case` (例如 `user-profile.js`)。
- **API 调用**:
    - 必须使用 `async/await` 语法处理 `/services` 中返回的 Promise 请求。
    - 必须使用 `try...catch` 结构捕获接口请求可能发生的错误。
- **setData 性能**:
    - **严禁**一次性 `setData` 一个巨大的对象或列表。
    - 更新列表中的单个项时，必须使用路径语法：`this.setData({ 'list[0].name': 'newName' })`。
    - 多个离散的 `setData` 调用应该合并为一次调用。
    - **提醒**: 在生成 `setData` 代码后，主动提醒开发者：“请确保没有将与界面渲染无关的数据放入 `setData`。”

## 2. WXML 模板规范

- **条件渲染**:
    - 对于需要频繁切换显示/隐藏状态的元素，使用 `hidden` 属性。
    - 对于运行时条件不满足，后续也不再需要渲染的元素，使用 `wx:if`。
- **列表渲染**:
    - 必须为 `wx:for` 指定唯一的 `wx:key`，并且 key 的值不能是 `index`，应使用列表项中唯一的 ID。
- **事件绑定**:
    - 事件绑定使用 `bind:tap` 或 `catch:tap`。优先使用 `bind`，除非需要阻止事件冒泡。
    - 事件处理函数命名为 `handle` 前缀，如 `handleItemTap`。

## 3. WXSS 样式规范

- **命名**:
    - 页面或组件的根节点 class，应与其文件名保持一致（`kebab-case`）。
    - 推荐使用 BEM (Block Element Modifier) 命名法，例如 `.user-card__avatar--large`。
- **单位**:
    - 必须使用 `rpx` (responsive pixel) 作为尺寸单位，以保证跨设备兼容性。
- **样式导入**:
    - 页面样式文件顶部应通过 `@import '/styles/main.wxss';` 引入全局公共样式。

## 4. Vant Weapp 使用规范

- **组件引入**:
    - **优先使用局部引入**: 在页面的 `.json` 文件中按需引入组件，而不是在 `app.json` 中全局引入。
    - **示例**: "为了优化页面加载性能，我将为您在 `some-page.json` 中局部引入 `van-button` 组件。"
- **样式覆盖**:
    - 如果需要覆盖 Vant 组件的默认样式，应使用外部样式类 (externalClasses) 或 CSS 变量。