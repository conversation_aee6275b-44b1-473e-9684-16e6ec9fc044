// API接口管理
import request from '../utils/request.js'
import config from '../config/index.js'

const api = {
  // 用户相关接口
  user: {
    // 登录
    login(data) {
      return request.post(config.api.endpoints.login, data)
    },
    
    // 登出
    logout() {
      return request.post(config.api.endpoints.logout)
    },
    
    // 获取用户信息
    getUserInfo() {
      return request.get(config.api.endpoints.userInfo)
    },
    
    // 更新用户信息
    updateUserInfo(data) {
      return request.put(config.api.endpoints.userInfo, data)
    }
  },

  // 农场相关接口
  farm: {
    // 获取农场信息
    getFarmInfo() {
      return request.get(config.api.endpoints.farmInfo)
    },
    
    // 获取农场统计数据
    getFarmStats() {
      return request.get(config.api.endpoints.farmStats)
    },
    
    // 更新农场信息
    updateFarmInfo(data) {
      return request.put(config.api.endpoints.farmInfo, data)
    }
  },

  // 任务相关接口
  task: {
    // 获取任务列表
    getTaskList(params = {}) {
      return request.get(config.api.endpoints.taskList, params)
    },
    
    // 获取任务详情
    getTaskDetail(id) {
      const url = config.api.endpoints.taskDetail.replace(':id', id)
      return request.get(url)
    },
    
    // 创建任务
    createTask(data) {
      return request.post(config.api.endpoints.taskList, data)
    },
    
    // 更新任务
    updateTask(id, data) {
      const url = config.api.endpoints.taskUpdate.replace(':id', id)
      return request.put(url, data)
    },
    
    // 删除任务
    deleteTask(id) {
      const url = config.api.endpoints.taskDetail.replace(':id', id)
      return request.delete(url)
    },
    
    // 完成任务
    completeTask(id) {
      const url = config.api.endpoints.taskUpdate.replace(':id', id)
      return request.put(url, { status: 'completed' })
    }
  },

  // 作物相关接口
  crop: {
    // 获取作物列表
    getCropList(params = {}) {
      return request.get(config.api.endpoints.cropList, params)
    },
    
    // 获取作物统计
    getCropStats() {
      return request.get(config.api.endpoints.cropStats)
    },
    
    // 创建作物记录
    createCrop(data) {
      return request.post(config.api.endpoints.cropList, data)
    },
    
    // 更新作物记录
    updateCrop(id, data) {
      return request.put(`${config.api.endpoints.cropList}/${id}`, data)
    },
    
    // 删除作物记录
    deleteCrop(id) {
      return request.delete(`${config.api.endpoints.cropList}/${id}`)
    }
  },

  // 设备相关接口
  device: {
    // 获取设备列表
    getDeviceList(params = {}) {
      return request.get(config.api.endpoints.deviceList, params)
    },
    
    // 获取设备状态
    getDeviceStatus(id) {
      const url = config.api.endpoints.deviceStatus.replace(':id', id)
      return request.get(url)
    },
    
    // 控制设备
    controlDevice(id, action, params = {}) {
      return request.post(`${config.api.endpoints.deviceList}/${id}/control`, {
        action,
        params
      })
    }
  },

  // 天气相关接口
  weather: {
    // 获取当前天气
    getCurrentWeather(location) {
      return request.get(config.api.endpoints.weather, { location })
    },
    
    // 获取天气预报
    getWeatherForecast(location, days = 7) {
      return request.get(`${config.api.endpoints.weather}/forecast`, {
        location,
        days
      })
    }
  },

  // 消息相关接口
  message: {
    // 获取消息列表
    getMessageList(params = {}) {
      return request.get(config.api.endpoints.messageList, params)
    },
    
    // 标记消息已读
    markMessageRead(id) {
      const url = config.api.endpoints.messageRead.replace(':id', id)
      return request.put(url)
    },
    
    // 批量标记已读
    markAllRead() {
      return request.put(`${config.api.endpoints.messageList}/read-all`)
    },
    
    // 删除消息
    deleteMessage(id) {
      return request.delete(`${config.api.endpoints.messageList}/${id}`)
    }
  },

  // 文件上传接口
  upload: {
    // 上传图片
    uploadImage(filePath, options = {}) {
      return request.upload('/upload/image', filePath, {
        name: 'image',
        ...options
      })
    },
    
    // 上传文件
    uploadFile(filePath, options = {}) {
      return request.upload('/upload/file', filePath, {
        name: 'file',
        ...options
      })
    }
  },

  // 统计相关接口
  stats: {
    // 获取总览统计
    getOverviewStats() {
      return request.get('/stats/overview')
    },
    
    // 获取作物统计
    getCropStats(params = {}) {
      return request.get('/stats/crops', params)
    },
    
    // 获取任务统计
    getTaskStats(params = {}) {
      return request.get('/stats/tasks', params)
    },
    
    // 获取设备统计
    getDeviceStats(params = {}) {
      return request.get('/stats/devices', params)
    }
  }
}

// 模拟数据（开发阶段使用）
const mockData = {
  // 农场统计数据
  farmStats: {
    machinery: { count: 16, inUse: 15 },
    inputs: { count: 54 },
    crops: { count: 3 },
    fieldArea: 383,
    totalRevenue: 9930.1
  },
  
  // 作物数据
  crops: [
    { name: '小麦', area: 5189.15, progress: 0.8 },
    { name: '玉米', area: 4888.79, progress: 0.65 },
    { name: '大豆', area: 4275.12, progress: 0.45 }
  ],
  
  // 任务数据
  tasks: [
    {
      id: 1,
      name: '小麦播种',
      date: '2025-04-01~2025-04-10',
      location: '小河口村1组（杨建国）',
      progress: 35.5,
      status: 'pending'
    },
    {
      id: 2,
      name: '小麦播种',
      date: '2025-04-01~2025-04-10',
      location: '小河口村2组（赵家德）',
      progress: 56.8,
      status: 'progress'
    },
    {
      id: 3,
      name: '小麦播种',
      date: '2025-04-01~2025-04-10',
      location: '小河口村3组（李松军）',
      progress: 60.0,
      status: 'progress'
    }
  ],
  
  // 消息数据
  messages: [
    {
      id: 1,
      title: '任务进度异常',
      content: '2025小河口玉米生产计划-耕整地：小河口村1组超期未开始，负责人：张建国15087651432；请尽快处理。',
      time: '2025-06-17 15:17',
      type: 'warning'
    },
    {
      id: 2,
      title: '任务进度异常',
      content: '2025小河口玉米生产计划-耕整地：小河口村2组超期未开始，负责人：张建国15087651433；请尽快处理。',
      time: '2025-06-17 15:17',
      type: 'warning'
    },
    {
      id: 3,
      title: '任务进度异常',
      content: '2025小河口玉米生产计划-耕整地：小河口村3组超期未开始，负责人：张建国15087651434；请尽快处理。',
      time: '2025-06-17 15:17',
      type: 'warning'
    }
  ]
}

// 开发环境使用模拟数据
if (config.env === 'development') {
  // 重写API方法返回模拟数据
  api.farm.getFarmStats = () => Promise.resolve({ data: mockData.farmStats })
  api.crop.getCropList = () => Promise.resolve({ data: mockData.crops })
  api.task.getTaskList = () => Promise.resolve({ data: mockData.tasks })
  api.message.getMessageList = () => Promise.resolve({ data: mockData.messages })
}

export default api
