// 我的页面
import api from '../../api/index.js'
import store from '../../utils/store.js'
import config from '../../config/index.js'
import { showToast, showConfirm, copyToClipboard } from '../../utils/common.js'

const app = getApp()

Page({
  data: {
    // 用户信息
    userInfo: {
      nickName: '王培乾',
      avatarUrl: '',
      account: '***78**56**',
      wechatId: '***10b9b8***'
    },
    
    // 用户统计数据
    userStats: {
      tasks: 12,
      reports: 8,
      records: 156,
      achievements: 5
    },
    
    // 版本信息
    version: config.version.app
  },

  // 页面加载
  onLoad: function (options) {
    console.log('Profile page loaded')
    this.initPage()
  },

  // 页面显示
  onShow: function () {
    console.log('Profile page shown')
    this.refreshUserInfo()
  },

  // 分享
  onShareAppMessage: function () {
    return {
      title: config.share.title,
      desc: config.share.desc,
      path: '/pages/index/index',
      imageUrl: config.share.imageUrl
    }
  },

  // 初始化页面
  initPage: function () {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '我的'
    })
    
    // 订阅store变化
    this.subscribeStore()
    
    // 加载用户信息
    this.loadUserInfo()
  },

  // 订阅store变化
  subscribeStore: function () {
    store.subscribe('user', (userData) => {
      if (userData.info) {
        this.setData({
          userInfo: {
            ...this.data.userInfo,
            ...userData.info
          }
        })
      }
    })
  },

  // 加载用户信息
  loadUserInfo: function () {
    // 从store获取用户信息
    const userInfo = store.getState('user.info')
    if (userInfo) {
      this.setData({
        userInfo: {
          ...this.data.userInfo,
          ...userInfo
        }
      })
    }
    
    // 从全局获取用户信息
    const globalUserInfo = app.globalData.userInfo
    if (globalUserInfo) {
      this.setData({
        userInfo: {
          ...this.data.userInfo,
          nickName: globalUserInfo.nickName,
          avatarUrl: globalUserInfo.avatarUrl
        }
      })
    }
  },

  // 刷新用户信息
  refreshUserInfo: function () {
    // 可以调用API获取最新的用户信息
    return Promise.resolve()
  },

  // 点击头像
  onAvatarTap: function () {
    if (!this.data.userInfo.avatarUrl) {
      // 获取用户信息
      app.getUserInfo((userInfo) => {
        this.setData({
          'userInfo.nickName': userInfo.nickName,
          'userInfo.avatarUrl': userInfo.avatarUrl
        })
        
        // 更新store
        store.setState({
          user: {
            info: {
              nickName: userInfo.nickName,
              avatarUrl: userInfo.avatarUrl
            }
          }
        })
      })
    } else {
      // 预览头像
      wx.previewImage({
        urls: [this.data.userInfo.avatarUrl],
        current: this.data.userInfo.avatarUrl
      })
    }
  },

  // 复制账号
  copyAccount: function () {
    const account = this.data.userInfo.account
    if (account && account !== '***78**56**') {
      copyToClipboard(account)
    } else {
      showToast('账号信息不完整')
    }
  },

  // 复制微信ID
  copyWechatId: function () {
    const wechatId = this.data.userInfo.wechatId
    if (wechatId && wechatId !== '***10b9b8***') {
      copyToClipboard(wechatId)
    } else {
      showToast('微信ID信息不完整')
    }
  },

  // 切换农场
  switchFarm: function () {
    wx.navigateTo({
      url: '/pages/farm-list/farm-list'
    })
  },

  // 通用导航方法
  navigateTo: function (e) {
    const url = e.currentTarget.dataset.url
    if (!url) {
      showToast('功能开发中')
      return
    }
    
    wx.navigateTo({ 
      url,
      fail: () => {
        showToast('页面不存在或开发中')
      }
    })
  },

  // 扫码功能
  scanCode: function () {
    wx.scanCode({
      success: (res) => {
        console.log('Scan result:', res)
        showToast('扫码成功')
        
        // 处理扫码结果
        this.handleScanResult(res.result)
      },
      fail: () => {
        showToast('扫码失败')
      }
    })
  },

  // 处理扫码结果
  handleScanResult: function (result) {
    if (result.startsWith('http')) {
      // 网页链接
      wx.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(result)}`
      })
    } else {
      // 其他类型的码
      wx.showModal({
        title: '扫码结果',
        content: result,
        showCancel: false
      })
    }
  },

  // 分享应用
  shareApp: function () {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
    
    showToast('请点击右上角分享')
  },

  // 联系客服
  contactService: function () {
    wx.makePhoneCall({
      phoneNumber: '************',
      fail: () => {
        // 如果拨打电话失败，显示客服信息
        wx.showModal({
          title: '联系客服',
          content: '客服电话：************\n工作时间：9:00-18:00',
          showCancel: false
        })
      }
    })
  },

  // 意见反馈
  feedback: function () {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    })
  },

  // 退出登录
  logout: function () {
    showConfirm('确定要退出登录吗？').then((confirmed) => {
      if (confirmed) {
        // 清除用户信息
        store.setState({
          user: {
            info: null,
            isLogin: false,
            token: null
          }
        })
        
        // 清除全局用户信息
        app.globalData.userInfo = null
        
        // 跳转到登录页面
        wx.redirectTo({
          url: '/pages/login/login'
        })
        
        showToast('已退出登录')
      }
    })
  },

  // 检查更新
  checkUpdate: function () {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          showToast('发现新版本')
        } else {
          showToast('当前已是最新版本')
        }
      })
      
      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })
      
      updateManager.onUpdateFailed(() => {
        showToast('新版本下载失败')
      })
    } else {
      showToast('当前微信版本过低，无法检查更新')
    }
  },

  // 清除缓存
  clearCache: function () {
    showConfirm('确定要清除缓存吗？').then((confirmed) => {
      if (confirmed) {
        try {
          wx.clearStorageSync()
          showToast('缓存已清除')
        } catch (error) {
          console.error('Clear cache failed:', error)
          showToast('清除缓存失败')
        }
      }
    })
  }
})
