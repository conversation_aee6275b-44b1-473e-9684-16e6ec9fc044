<!-- 我的页面 -->
<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-info">
      <image class="user-avatar" 
             src="{{userInfo.avatarUrl || '/assets/images/default-avatar.png'}}" 
             mode="aspectFill"
             bindtap="onAvatarTap"></image>
      
      <view class="user-details">
        <view class="user-name">{{userInfo.nickName || '王培乾'}}</view>
        <view class="user-id">账号：{{userInfo.account || '***78**56**'}}</view>
        <view class="user-wechat">微信appid：{{userInfo.wechatId || '***10b9b8***'}}</view>
      </view>
      
      <view class="copy-buttons">
        <view class="copy-btn" bindtap="copyAccount">复制</view>
        <view class="copy-btn" bindtap="copyWechatId">复制</view>
      </view>
    </view>
  </view>

  <!-- 切换农场 -->
  <view class="farm-switch">
    <view class="farm-switch-item" bindtap="switchFarm">
      <image class="farm-switch-icon" src="/assets/icons/switch.png" mode="aspectFit"></image>
      <text class="farm-switch-text">切换农场</text>
      <image class="farm-switch-arrow" src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="navigateTo" data-url="/pages/settings/settings">
      <view class="menu-item-left">
        <image class="menu-icon" src="/assets/icons/settings.png" mode="aspectFit"></image>
        <text class="menu-text">设置</text>
      </view>
      <image class="menu-arrow" src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
    </view>
    
    <view class="menu-item" bindtap="navigateTo" data-url="/pages/help/help">
      <view class="menu-item-left">
        <image class="menu-icon" src="/assets/icons/help.png" mode="aspectFit"></image>
        <text class="menu-text">帮助与反馈</text>
      </view>
      <image class="menu-arrow" src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
    </view>
    
    <view class="menu-item" bindtap="navigateTo" data-url="/pages/about/about">
      <view class="menu-item-left">
        <image class="menu-icon" src="/assets/icons/about.png" mode="aspectFit"></image>
        <text class="menu-text">关于我们</text>
      </view>
      <image class="menu-arrow" src="/assets/icons/arrow-right.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 数据统计 -->
  <view class="stats-section">
    <view class="section-title">我的数据</view>
    
    <view class="stats-grid">
      <view class="stats-item" bindtap="navigateTo" data-url="/pages/my-tasks/my-tasks">
        <view class="stats-value">{{userStats.tasks}}</view>
        <view class="stats-label">我的任务</view>
      </view>
      
      <view class="stats-item" bindtap="navigateTo" data-url="/pages/my-reports/my-reports">
        <view class="stats-value">{{userStats.reports}}</view>
        <view class="stats-label">我的报告</view>
      </view>
      
      <view class="stats-item" bindtap="navigateTo" data-url="/pages/my-records/my-records">
        <view class="stats-value">{{userStats.records}}</view>
        <view class="stats-label">操作记录</view>
      </view>
      
      <view class="stats-item" bindtap="navigateTo" data-url="/pages/my-achievements/my-achievements">
        <view class="stats-value">{{userStats.achievements}}</view>
        <view class="stats-label">我的成就</view>
      </view>
    </view>
  </view>

  <!-- 快捷工具 -->
  <view class="tools-section">
    <view class="section-title">快捷工具</view>
    
    <view class="tools-grid">
      <view class="tool-item" bindtap="scanCode">
        <image class="tool-icon" src="/assets/icons/scan.png" mode="aspectFit"></image>
        <text class="tool-text">扫一扫</text>
      </view>
      
      <view class="tool-item" bindtap="shareApp">
        <image class="tool-icon" src="/assets/icons/share.png" mode="aspectFit"></image>
        <text class="tool-text">分享应用</text>
      </view>
      
      <view class="tool-item" bindtap="contactService">
        <image class="tool-icon" src="/assets/icons/service.png" mode="aspectFit"></image>
        <text class="tool-text">联系客服</text>
      </view>
      
      <view class="tool-item" bindtap="feedback">
        <image class="tool-icon" src="/assets/icons/feedback.png" mode="aspectFit"></image>
        <text class="tool-text">意见反馈</text>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">版本 {{version}}</text>
  </view>
</view>
